# Inventory Management System REST API Documentation

## Overview

This document provides comprehensive documentation for the Inventory Management System REST API, designed to support mobile applications with full feature parity to the web interface (except label generation).

## Base Information

- **Base URL**: `http://localhost:8000/api/v1/`
- **API Version**: 1.0.0
- **Authentication**: JWT Bearer <PERSON>ken
- **Content Type**: `application/json`
- **Documentation**: 
  - Swagger UI: `http://localhost:8000/api/v1/docs/`
  - ReDoc: `http://localhost:8000/api/v1/redoc/`
  - OpenAPI Schema: `http://localhost:8000/api/v1/schema/`

## Authentication

### JWT Token Authentication

The API uses JWT (JSON Web Token) authentication. All requests (except guest comments) require a valid JWT token.

#### Obtaining Tokens

**POST** `/auth/token/`

```json
{
  "username": "your_username",
  "password": "your_password"
}
```

**Response:**
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": 1,
    "username": "marshall",
    "email": "<EMAIL>",
    "first_name": "Marshall",
    "last_name": "Test",
    "is_superuser": false
  },
  "organizations": [
    {
      "id": 1,
      "name": "Default Organization",
      "code": "DEFAULT",
      "permissions": {
        "is_admin": true,
        "can_edit": true,
        "can_add": true
      }
    }
  ]
}
```

#### Using Tokens

Include the access token in the Authorization header:
```
Authorization: Bearer <access_token>
```

#### Organization Context

For multi-organization users, specify the organization using the header:
```
X-Organization-ID: <organization_id>
```

#### Token Refresh

**POST** `/auth/token/refresh/`

```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

## Core Endpoints

### User Management

#### Get Current User
**GET** `/auth/user/`

Returns current user information.

#### Update User Profile
**PATCH** `/auth/user/`

```json
{
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>"
}
```

#### Get User Organizations
**GET** `/auth/organizations/`

Returns organizations the user has access to.

### Items Management

#### List Items
**GET** `/items/`

**Query Parameters:**
- `page`: Page number (default: 1)
- `page_size`: Items per page (default: 20, max: 100)
- `search`: Search in name, description
- `item_type`: Filter by item type ID
- `status`: Filter by status ID
- `has_location`: Filter items with/without location
- `is_archived`: Include archived items
- `ordering`: Sort by field (e.g., `-last_updated`)

#### Get Item Details
**GET** `/items/{id}/`

#### Create Item
**POST** `/items/`

```json
{
  "item_name": "New Item",
  "item_description": "Item description",
  "item_type_id": 1,
  "status_id": 2,
  "located_in_code": "ae6f33",
  "image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
  "custom_fields": {}
}
```

#### Update Item
**PATCH** `/items/{id}/`

#### Archive/Unarchive Item
**POST** `/items/{id}/archive/`
**POST** `/items/{id}/unarchive/`

#### Get Item by Code
**GET** `/items/by_code/?code={item_code}`

#### Get Item by EPC
**GET** `/items/by_epc/?epc={epc_hex}`

**Query Parameters:**
- `epc`: 24-character hexadecimal EPC string (96 bits)

**Example:**
```
GET /items/by_epc/?epc=100000000000000000AE6F33
```

#### Get Item Comments
**GET** `/items/{id}/comments/`

#### Get Contained Items
**GET** `/items/{id}/contained_items/`

### Mobile-Specific Endpoints

#### Scan Item
**POST** `/mobile/scan/`

Supports both barcode and RFID/EPC scanning.

**Request Body:**
```json
{
  "scan_data": "ae6f33",        // For barcode scanning
  "scan_type": "code"
}
```

```json
{
  "scan_data": "100000000000000000AE6F33",  // For EPC scanning
  "scan_type": "epc"
}
```

**Response:**
```json
{
  "found": true,
  "item": {
    "id": 1,
    "item_code": "ae6f33",
    "item_name": "Rack",
    "epc_hex": "100000000000000000AE6F33",
    // ... full item details
  }
}
```

**Not Found Response:**
```json
{
  "found": false,
  "scan_data": "999999999999999999999999",
  "scan_type": "epc",
  "message": "No item found for epc: 999999999999999999999999"
}
```

#### Bulk Create Items
**POST** `/mobile/bulk-create/`

```json
{
  "items": [
    {
      "item_name": "Item 1",
      "item_description": "Description 1"
    },
    {
      "item_name": "Item 2",
      "item_description": "Description 2"
    }
  ]
}
```

**Response:**
```json
{
  "created_count": 2,
  "error_count": 0,
  "created_items": [...],
  "errors": []
}
```

### Comments System

#### List Comments
**GET** `/comments/`

**Query Parameters:**
- `item`: Filter by item ID
- `item_code`: Filter by item code
- `is_guest_comment`: Filter guest/authenticated comments
- `has_photo`: Filter comments with photos
- `include_archived`: Include archived comments

#### Create Comment
**POST** `/comments/`

```json
{
  "item": 1,
  "comment_text": "This item needs repair",
  "reason": 6,
  "photo": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
  "guest_name": "Anonymous User"  // For guest comments only
}
```

#### Archive/Unarchive Comment
**POST** `/comments/{id}/archive/`
**POST** `/comments/{id}/unarchive/`

### Guest Comments

Guest users can create comments without authentication by omitting the Authorization header and providing a `guest_name`:

```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"item": 1, "comment_text": "Guest comment", "guest_name": "Anonymous", "reason": 6}' \
  "http://localhost:8000/api/v1/comments/?organization_id=1"
```

### Barcode Generation

#### Generate Item Barcode
**GET** `/items/{item_code}/barcode/`

**Query Parameters:**
- `format`: Barcode format (`datamatrix`, `qr`, `code128`, `auto`)
- `size`: Image size in pixels (default: 200)

Returns PNG image.

#### Generate Item QR Code
**GET** `/items/{item_code}/qr/`

**Query Parameters:**
- `size`: Image size in pixels (default: 200)

Returns PNG image with item URL.

### Organizations

#### List Organizations
**GET** `/organizations/`

Returns organizations the user has access to.

### Managed Lists

#### Get Managed List Values
**GET** `/managed-lists/`

**Query Parameters:**
- `list_name`: Filter by list name (e.g., `ItemTypes`, `ItemStatuses`, `CommentReasons`)

## Data Models

### Item
```json
{
  "id": 1,
  "item_code": "ae6f33",
  "epc_hex": "10000000000000000AE6F33",
  "item_name": "Rack",
  "item_description": "Storage rack",
  "item_type": {
    "id": 1,
    "list_name": "ItemTypes",
    "value": "Default",
    "is_active": true
  },
  "status": {
    "id": 2,
    "list_name": "ItemStatuses", 
    "value": "Available",
    "is_active": true
  },
  "located_in": {
    "id": 2,
    "item_code": "0d1283",
    "item_name": "Room"
  },
  "organization": {
    "id": 1,
    "name": "Default Organization",
    "code": "DEFAULT"
  },
  "image": null,
  "image_caption": "",
  "primary_image": "http://localhost:8000/media/item_images/image.jpg",
  "custom_fields": {},
  "date_added": "2025-05-17T21:36:36.485581-04:00",
  "last_updated": "2025-05-22T09:48:36.165799-04:00",
  "is_archived": false,
  "contained_item_count": 1
}
```

### Comment
```json
{
  "id": 1,
  "item": 1,
  "comment_text": "This item needs repair",
  "reason": 6,
  "photo": "http://localhost:8000/media/comment_photos/photo.jpg",
  "user": 1,
  "guest_name": "",
  "commenter_name": "marshall",
  "is_guest_comment": false,
  "created_at": "2025-06-19T16:51:01.204879-04:00",
  "updated_at": "2025-06-19T16:51:01.204895-04:00",
  "is_archived": false,
  "archived_at": null,
  "archived_by": null
}
```

## Error Handling

### Standard Error Response
```json
{
  "error": "Error type",
  "detail": "Detailed error message"
}
```

### Common HTTP Status Codes
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `500`: Internal Server Error

### Validation Errors
```json
{
  "field_name": ["Error message for this field"],
  "another_field": ["Another error message"]
}
```

## Rate Limiting

- **Scan endpoint**: 100 requests per hour per IP
- **Bulk create**: 50 requests per hour per user
- **General endpoints**: No specific limits (server default)

## Image Handling

### Base64 Image Upload
Images can be uploaded as base64-encoded strings:

```json
{
  "image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
}
```

### Supported Formats
- **Item images**: JPG, PNG, GIF
- **Comment photos**: JPG only (10MB limit with compression)

## Pagination

All list endpoints support pagination:

```json
{
  "count": 100,
  "next": "http://localhost:8000/api/v1/items/?page=3",
  "previous": "http://localhost:8000/api/v1/items/?page=1", 
  "results": [...]
}
```

## Mobile Development Guidelines

### Performance Optimization
1. Use the lightweight list serializer for item lists
2. Implement pagination for large datasets
3. Cache organization data locally
4. Use appropriate page sizes (20-50 items)

### Offline Considerations
1. Store JWT tokens securely
2. Cache frequently accessed data
3. Implement retry logic for failed requests
4. Handle network connectivity changes

### Security Best Practices
1. Store JWT tokens in secure storage
2. Implement token refresh logic
3. Validate all user inputs
4. Use HTTPS in production

### Error Handling
1. Implement comprehensive error handling
2. Provide user-friendly error messages
3. Handle network timeouts gracefully
4. Log errors for debugging

## Testing

### Example API Calls

```bash
# Get access token
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"username": "your_username", "password": "your_password"}' \
  http://localhost:8000/api/v1/auth/token/

# List items
curl -H "Authorization: Bearer <token>" \
  -H "X-Organization-ID: 1" \
  http://localhost:8000/api/v1/items/

# Get item by code
curl -H "Authorization: Bearer <token>" \
  -H "X-Organization-ID: 1" \
  http://localhost:8000/api/v1/items/by_code/?code=ae6f33

# Get item by EPC
curl -H "Authorization: Bearer <token>" \
  -H "X-Organization-ID: 1" \
  http://localhost:8000/api/v1/items/by_epc/?epc=100000000000000000AE6F33

# Scan item by barcode
curl -X POST \
  -H "Authorization: Bearer <token>" \
  -H "X-Organization-ID: 1" \
  -H "Content-Type: application/json" \
  -d '{"scan_data": "ae6f33", "scan_type": "code"}' \
  http://localhost:8000/api/v1/mobile/scan/

# Scan item by EPC
curl -X POST \
  -H "Authorization: Bearer <token>" \
  -H "X-Organization-ID: 1" \
  -H "Content-Type: application/json" \
  -d '{"scan_data": "100000000000000000AE6F33", "scan_type": "epc"}' \
  http://localhost:8000/api/v1/mobile/scan/
```

## Support

For technical support or questions about the API:
- Review the interactive documentation at `/api/v1/docs/`
- Check the OpenAPI schema at `/api/v1/schema/`
- Contact the development team

## Implementation Roadmap

### Phase 1: Core Functionality (Week 1-2)
1. **Authentication System**
   - JWT token management
   - Organization selection
   - User profile management

2. **Item Management**
   - List/search items
   - View item details
   - Create/edit items
   - Archive/unarchive items

### Phase 2: Mobile Features (Week 3-4)
1. **Scanning Functionality**
   - Barcode scanner integration
   - RFID scanner support
   - Item lookup by scan

2. **Bulk Operations**
   - Bulk item creation
   - Batch processing
   - Progress indicators

### Phase 3: Comments & Media (Week 5-6)
1. **Comment System**
   - View/create comments
   - Guest comment support
   - Comment archiving

2. **Photo Management**
   - Camera integration
   - Photo upload/compression
   - Image display

### Phase 4: Advanced Features (Week 7-8)
1. **Offline Support**
   - Data caching
   - Sync mechanisms
   - Conflict resolution

2. **Performance Optimization**
   - Image optimization
   - Network efficiency
   - Battery optimization

## Development Priorities

### High Priority
- [ ] Authentication and token management
- [ ] Item listing and search
- [ ] Barcode/RFID scanning
- [ ] Item creation and editing

### Medium Priority
- [ ] Comment system
- [ ] Photo upload
- [ ] Bulk operations
- [ ] Organization management

### Low Priority
- [ ] Advanced filtering
- [ ] Offline synchronization
- [ ] Push notifications
- [ ] Analytics integration

---

**Last Updated**: June 19, 2025
**API Version**: 1.0.0
