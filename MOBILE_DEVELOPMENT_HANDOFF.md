# Mobile Development Handoff Document

## Project Overview

The Inventory Management System (IMS) mobile application provides full feature parity with the web interface, enabling users to manage inventory items, scan barcodes/RFID tags, and collaborate through comments.

## Technical Specifications

### API Integration
- **Base URL**: `http://localhost:8000/api/v1/`
- **Authentication**: JW<PERSON>
- **Data Format**: JSON
- **Documentation**: Available at `/api/v1/docs/`

### Required Hardware Support
- **Camera**: For barcode scanning and photo capture
- **NFC/RFID**: For RFID tag scanning (optional)
- **Network**: WiFi and cellular data support

### Supported Platforms
- **iOS**: 14.0+
- **Android**: API level 24+ (Android 7.0)

## Core Features

### 1. Authentication & User Management
- JWT-based authentication
- Multi-organization support
- User profile management
- Secure token storage

### 2. Item Management
- Browse and search inventory items
- View detailed item information
- Create and edit items
- Archive/unarchive items
- Hierarchical item relationships (containers)

### 3. Scanning Capabilities
- Barcode scanning (Code128, QR codes)
- RFID/EPC scanning
- Real-time item lookup
- Scan-to-create workflow

### 4. Comment System
- View item comments
- Add comments with photos
- Guest comment support
- Comment archiving
- Reason categorization

### 5. Bulk Operations
- Bulk item creation
- Batch processing with progress tracking
- Error handling for individual items

## API Endpoints Summary

### Authentication
- `POST /auth/token/` - Login and get tokens
- `POST /auth/token/refresh/` - Refresh access token
- `GET /auth/user/` - Get current user info
- `GET /auth/organizations/` - Get user organizations

### Items
- `GET /items/` - List items with filtering/search
- `GET /items/{id}/` - Get item details
- `POST /items/` - Create new item
- `PATCH /items/{id}/` - Update item
- `GET /items/by_code/?code={code}` - Find item by code

### Mobile-Specific
- `POST /mobile/scan/` - Scan barcode/RFID
- `POST /mobile/bulk-create/` - Bulk create items

### Comments
- `GET /comments/` - List comments
- `POST /comments/` - Create comment (supports guest users)
- `POST /comments/{id}/archive/` - Archive comment

### Barcodes
- `GET /items/{code}/barcode/` - Generate barcode image
- `GET /items/{code}/qr/` - Generate QR code

## Data Models

### Item
```typescript
interface Item {
  id: number;
  item_code: string;
  epc_hex?: string;
  item_name: string;
  item_description?: string;
  item_type?: ManagedListValue;
  status?: ManagedListValue;
  located_in?: ItemReference;
  organization: Organization;
  image?: string;
  primary_image?: string;
  custom_fields: Record<string, any>;
  date_added: string;
  last_updated: string;
  is_archived: boolean;
  contained_item_count: number;
}
```

### Comment
```typescript
interface Comment {
  id: number;
  item: number;
  comment_text: string;
  reason?: number;
  photo?: string;
  user?: number;
  guest_name?: string;
  commenter_name: string;
  is_guest_comment: boolean;
  created_at: string;
  updated_at: string;
  is_archived: boolean;
}
```

## Implementation Guidelines

### Authentication Flow
1. User enters credentials
2. App calls `/auth/token/` endpoint
3. Store access and refresh tokens securely
4. Include `Authorization: Bearer <token>` in all requests
5. Handle token refresh automatically
6. For multi-org users, set `X-Organization-ID` header

### Scanning Implementation
1. Integrate camera/barcode scanning library
2. Call `/mobile/scan/` with scanned data
3. Handle found/not found responses
4. Provide scan-to-create workflow for new items

### Photo Upload
1. Capture photo using device camera
2. Compress image (max 10MB for comments)
3. Convert to base64 format
4. Include in API request as `data:image/jpeg;base64,...`

### Error Handling
- Implement retry logic for network failures
- Show user-friendly error messages
- Handle validation errors from API
- Log errors for debugging

### Performance Considerations
- Use pagination for large item lists
- Implement image caching
- Lazy load item details
- Optimize network requests

## Security Requirements

### Data Protection
- Store JWT tokens in secure storage (Keychain/Keystore)
- Implement certificate pinning for production
- Validate all user inputs
- Use HTTPS for all API calls

### User Privacy
- Request minimal permissions
- Secure photo storage
- Clear data on logout
- Handle guest user data appropriately

## Testing Strategy

### Unit Tests
- API client functionality
- Data model validation
- Authentication logic
- Scanning utilities

### Integration Tests
- End-to-end user flows
- API integration
- Camera/scanning functionality
- Photo upload/download

### User Acceptance Tests
- Core user workflows
- Scanning accuracy
- Performance benchmarks
- Accessibility compliance

## Development Environment Setup

### API Server
```bash
# Clone repository
git clone <repository-url>
cd ims

# Install dependencies
pip install -r requirements.txt

# Run migrations
python manage.py migrate

# Create test user
python manage.py createsuperuser

# Start development server
python manage.py runserver
```

### Test Data
- Use management command to create test items
- Generate sample barcodes for testing
- Create test organizations and users

## Deployment Considerations

### Production API
- Update base URL to production server
- Implement proper SSL/TLS
- Configure rate limiting
- Set up monitoring and logging

### App Store Requirements
- Implement proper permission requests
- Add privacy policy
- Handle app store review guidelines
- Prepare app metadata and screenshots

## Support and Maintenance

### Documentation
- API documentation: `/api/v1/docs/`
- OpenAPI schema: `/api/v1/schema/`
- This handoff document

### Monitoring
- API usage analytics
- Error tracking and reporting
- Performance monitoring
- User feedback collection

### Updates
- API versioning strategy
- Backward compatibility
- Feature flag implementation
- Gradual rollout process

## Contact Information

### Development Team
- **Backend API**: [Contact information]
- **Mobile Development**: [Contact information]
- **DevOps/Infrastructure**: [Contact information]

### Resources
- **API Documentation**: http://localhost:8000/api/v1/docs/
- **Project Repository**: [Repository URL]
- **Issue Tracking**: [Issue tracker URL]
- **Slack Channel**: [Slack channel]

---

**Document Version**: 1.0
**Last Updated**: June 19, 2025
**Next Review**: July 19, 2025
