version: '3'

services:
  web:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
    volumes:
      - ..:/workspace
    command: sleep infinity
    environment:
      - DEBUG=True
      - DATABASE_URL=************************************/inventory
    depends_on:
      - db
  
  db:
    image: postgres:14
    restart: unless-stopped
    volumes:
      - postgres-data:/var/lib/postgresql/data
    environment:
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_DB=inventory

volumes:
  postgres-data:
