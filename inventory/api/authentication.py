"""
Custom authentication classes for the Inventory Management System API.

This module provides JWT-based authentication with organization context
and custom token management for mobile applications.
"""

from rest_framework_simplejwt.authentication import J<PERSON><PERSON>uthentication
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer, TokenRefreshSerializer
from rest_framework import serializers
from django.contrib.auth.models import User
from ..models import Organization, OrganizationUser


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    """
    Custom JWT token serializer that includes organization information
    and user permissions in the token response.
    """
    
    def validate(self, attrs):
        """Validate credentials and add organization context."""
        data = super().validate(attrs)
        
        # Get user's organizations
        user_orgs = OrganizationUser.objects.filter(
            user=self.user
        ).select_related('organization').filter(
            organization__is_active=True
        )
        
        # Add user and organization information to response
        data.update({
            'user': {
                'id': self.user.id,
                'username': self.user.username,
                'email': self.user.email,
                'first_name': self.user.first_name,
                'last_name': self.user.last_name,
                'is_superuser': self.user.is_superuser,
            },
            'organizations': [
                {
                    'id': org_user.organization.id,
                    'name': org_user.organization.name,
                    'code': org_user.organization.code,
                    'permissions': {
                        'is_admin': org_user.is_admin,
                        'can_edit': org_user.can_edit,
                        'can_add': org_user.can_add,
                    }
                }
                for org_user in user_orgs
            ]
        })
        
        return data


# Views are moved to auth_views.py to avoid circular imports


class CustomTokenRefreshSerializer(TokenRefreshSerializer):
    """
    Custom token refresh serializer that maintains organization context.
    """
    
    def validate(self, attrs):
        """Validate refresh token and maintain user context."""
        data = super().validate(attrs)
        
        # Get user from the refresh token
        refresh_token = self.token_class(attrs['refresh'])
        user_id = refresh_token.payload.get('user_id')
        
        if user_id:
            try:
                user = User.objects.get(id=user_id)
                
                # Get user's organizations
                user_orgs = OrganizationUser.objects.filter(
                    user=user
                ).select_related('organization').filter(
                    organization__is_active=True
                )
                
                # Add user and organization information to response
                data.update({
                    'user': {
                        'id': user.id,
                        'username': user.username,
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'is_superuser': user.is_superuser,
                    },
                    'organizations': [
                        {
                            'id': org_user.organization.id,
                            'name': org_user.organization.name,
                            'code': org_user.organization.code,
                            'permissions': {
                                'is_admin': org_user.is_admin,
                                'can_edit': org_user.can_edit,
                                'can_add': org_user.can_add,
                            }
                        }
                        for org_user in user_orgs
                    ]
                })
            except User.DoesNotExist:
                pass
        
        return data


class OrganizationAwareJWTAuthentication(JWTAuthentication):
    """
    Custom JWT authentication that adds organization context to the request.
    """

    def authenticate(self, request):
        """Authenticate and add organization context."""
        result = super().authenticate(request)

        if result is not None:
            user, _token = result

            # Add organization context if specified in headers
            org_id = request.META.get('HTTP_X_ORGANIZATION_ID')
            if org_id:
                try:
                    org_id = int(org_id)
                    if user.is_superuser:
                        # Superusers can access any organization
                        organization = Organization.objects.get(
                            id=org_id,
                            is_active=True
                        )
                        request.organization = organization
                    else:
                        # Regular users need membership
                        org_user = OrganizationUser.objects.get(
                            user=user,
                            organization_id=org_id,
                            organization__is_active=True
                        )
                        request.organization = org_user.organization
                except (ValueError, TypeError, Organization.DoesNotExist, OrganizationUser.DoesNotExist):
                    # Invalid organization ID or no access
                    pass

        return result


class GuestTokenSerializer(serializers.Serializer):
    """
    Serializer for creating guest tokens for comment posting.
    """
    guest_name = serializers.CharField(max_length=100)
    organization_id = serializers.IntegerField()
    
    def validate_organization_id(self, value):
        """Validate that the organization exists and is active."""
        try:
            Organization.objects.get(id=value, is_active=True)
            return value
        except Organization.DoesNotExist:
            raise serializers.ValidationError("Invalid organization")
    
    def create(self, validated_data):
        """Create a temporary guest token."""
        # For guest tokens, we'll use a simple approach
        # In a production system, you might want to implement
        # a more sophisticated guest token system
        return {
            'guest_name': validated_data['guest_name'],
            'organization_id': validated_data['organization_id'],
            'token_type': 'guest'
        }
