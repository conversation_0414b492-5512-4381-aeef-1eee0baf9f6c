"""
Custom throttling classes for the Inventory Management System API.

This module provides specialized throttling for different API endpoints
to ensure fair usage and prevent abuse.
"""

from rest_framework.throttling import UserRateThrottle, AnonRateThrottle


class MobileScanThrottle(UserRateThrottle):
    """
    Throttle for mobile scanning endpoints.
    Allows 100 scans per hour per user.
    """
    scope = 'mobile_scan'
    rate = '100/hour'


class MobileBulkThrottle(UserRateThrottle):
    """
    Throttle for mobile bulk operations.
    Allows 50 bulk operations per hour per user.
    """
    scope = 'mobile_bulk'
    rate = '50/hour'


class CommentCreateThrottle(UserRateThrottle):
    """
    Throttle for comment creation.
    Allows 200 comments per hour per user.
    """
    scope = 'comment_create'
    rate = '200/hour'


class GuestCommentThrottle(AnonRateThrottle):
    """
    Throttle for guest comment creation.
    Allows 50 comments per hour per IP address.
    """
    scope = 'guest_comment'
    rate = '50/hour'


class BarcodeGenerationThrottle(UserRateThrottle):
    """
    Throttle for barcode generation endpoints.
    Allows 500 barcode generations per hour per user.
    """
    scope = 'barcode_generation'
    rate = '500/hour'
