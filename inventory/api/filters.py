"""
Filters for the Inventory Management System API.

This module contains filter classes for advanced filtering
of API endpoints using django-filter.
"""

import django_filters
from django.db.models import Q
from ..models import Item, ItemComment, ManagedListValue


class ItemFilter(django_filters.FilterSet):
    """
    Filter class for Item model with advanced filtering options.
    """
    # Text search across multiple fields
    search = django_filters.CharFilter(method='filter_search', label='Search')
    item_code = django_filters.CharFilter(method='filter_item_code', label='Item Code')

    # Date range filters
    date_added_after = django_filters.DateTimeFilter(field_name='date_added', lookup_expr='gte')
    date_added_before = django_filters.DateTimeFilter(field_name='date_added', lookup_expr='lte')
    last_updated_after = django_filters.DateTimeFilter(field_name='last_updated', lookup_expr='gte')
    last_updated_before = django_filters.DateTimeFilter(field_name='last_updated', lookup_expr='lte')
    
    # Status and type filters
    item_type = django_filters.ModelChoiceFilter(
        queryset=ManagedListValue.objects.filter(list_name='ItemTypes', is_active=True)
    )
    status = django_filters.ModelChoiceFilter(
        queryset=ManagedListValue.objects.filter(list_name='ItemStatuses', is_active=True)
    )
    
    # Location filters
    located_in = django_filters.ModelChoiceFilter(queryset=Item.objects.all())
    has_location = django_filters.BooleanFilter(method='filter_has_location')
    
    # Archive status
    is_archived = django_filters.BooleanFilter()
    include_archived = django_filters.BooleanFilter(method='filter_include_archived')
    
    # Container status
    is_container = django_filters.BooleanFilter(method='filter_is_container')
    has_contained_items = django_filters.BooleanFilter(method='filter_has_contained_items')
    
    # Image filters
    has_image = django_filters.BooleanFilter(method='filter_has_image')
    
    class Meta:
        model = Item
        fields = {
            'item_name': ['exact', 'icontains', 'istartswith'],
            'item_description': ['icontains'],
        }
        # Override for JSONField
        filter_overrides = {
            'django.db.models.JSONField': {
                'filter_class': django_filters.CharFilter,
                'extra': lambda f: {
                    'lookup_expr': 'icontains',
                },
            },
        }
    
    def filter_search(self, queryset, _name, value):
        """
        Perform full-text search across multiple fields.
        """
        if not value:
            return queryset

        # Search in text fields and custom fields
        text_query = Q(item_name__icontains=value) | Q(item_description__icontains=value)

        # Try to search by item code if the value looks like hex
        try:
            if len(value) >= 4 and all(c in '0123456789abcdefABCDEF' for c in value):
                # Convert hex string to binary for lookup
                binary_code = bytes.fromhex(value.upper())
                text_query |= Q(_item_code=binary_code)
        except ValueError:
            pass

        return queryset.filter(text_query)

    def filter_item_code(self, queryset, _name, value):
        """Filter by item code (hex string)."""
        if not value:
            return queryset

        try:
            # Convert hex string to binary for lookup
            binary_code = bytes.fromhex(value.upper())
            return queryset.filter(_item_code=binary_code)
        except ValueError:
            return queryset.none()

    def filter_has_location(self, queryset, _name, value):
        """Filter items based on whether they have a location."""
        if value is True:
            return queryset.filter(located_in__isnull=False)
        elif value is False:
            return queryset.filter(located_in__isnull=True)
        return queryset

    def filter_include_archived(self, queryset, _name, value):
        """Include or exclude archived items."""
        if value is False:
            return queryset.filter(is_archived=False)
        return queryset

    def filter_is_container(self, queryset, _name, value):
        """Filter items that can contain other items."""
        if value is True:
            return queryset.filter(contained_items__isnull=False).distinct()
        elif value is False:
            return queryset.filter(contained_items__isnull=True)
        return queryset

    def filter_has_contained_items(self, queryset, _name, value):
        """Filter items based on whether they contain other items."""
        if value is True:
            return queryset.filter(
                contained_items__isnull=False,
                contained_items__is_archived=False
            ).distinct()
        elif value is False:
            return queryset.filter(
                Q(contained_items__isnull=True) |
                Q(contained_items__is_archived=True)
            ).distinct()
        return queryset

    def filter_has_image(self, queryset, _name, value):
        """Filter items based on whether they have an image."""
        if value is True:
            return queryset.exclude(image='')
        elif value is False:
            return queryset.filter(image='')
        return queryset


class ItemCommentFilter(django_filters.FilterSet):
    """
    Filter class for ItemComment model.
    """
    # Date range filters
    created_after = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='gte')
    created_before = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='lte')
    updated_after = django_filters.DateTimeFilter(field_name='updated_at', lookup_expr='gte')
    updated_before = django_filters.DateTimeFilter(field_name='updated_at', lookup_expr='lte')
    
    # Item filters
    item = django_filters.ModelChoiceFilter(queryset=Item.objects.all())
    item_code = django_filters.CharFilter(method='filter_by_item_code')
    
    # User filters
    is_guest_comment = django_filters.BooleanFilter(method='filter_is_guest_comment')
    commenter = django_filters.CharFilter(method='filter_commenter')
    
    # Archive status
    is_archived = django_filters.BooleanFilter()
    include_archived = django_filters.BooleanFilter(method='filter_include_archived')
    
    # Photo filter
    has_photo = django_filters.BooleanFilter(method='filter_has_photo')
    
    class Meta:
        model = ItemComment
        fields = {
            'comment_text': ['icontains'],
            'reason': ['exact'],
            'guest_name': ['exact', 'icontains'],
        }
    
    def filter_by_item_code(self, queryset, _name, value):
        """Filter comments by item code."""
        if not value:
            return queryset

        try:
            # Convert hex string to binary for lookup
            binary_code = bytes.fromhex(value.upper())
            return queryset.filter(item___item_code=binary_code)
        except ValueError:
            return queryset.none()

    def filter_is_guest_comment(self, queryset, _name, value):
        """Filter comments based on whether they are guest comments."""
        if value is True:
            return queryset.filter(user__isnull=True)
        elif value is False:
            return queryset.filter(user__isnull=False)
        return queryset

    def filter_commenter(self, queryset, _name, value):
        """Filter comments by commenter name (user or guest)."""
        if not value:
            return queryset

        return queryset.filter(
            Q(user__username__icontains=value) |
            Q(user__first_name__icontains=value) |
            Q(user__last_name__icontains=value) |
            Q(guest_name__icontains=value)
        )

    def filter_include_archived(self, queryset, _name, value):
        """Include or exclude archived comments."""
        if value is False:
            return queryset.filter(is_archived=False)
        return queryset

    def filter_has_photo(self, queryset, _name, value):
        """Filter comments based on whether they have a photo."""
        if value is True:
            return queryset.exclude(photo='')
        elif value is False:
            return queryset.filter(photo='')
        return queryset
