"""
Authentication views for the Inventory Management System API.

This module contains the JWT authentication views that were separated
from the authentication module to avoid circular imports.
"""

from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework import serializers, status
from rest_framework.response import Response
from .authentication import CustomTokenObtainPairSerializer, CustomTokenRefreshSerializer


class CustomTokenObtainPairView(TokenObtainPairView):
    """
    Custom token obtain view with enhanced error handling
    and organization context.
    """
    serializer_class = CustomTokenObtainPairSerializer
    
    def post(self, request, *args, **kwargs):
        """Handle token creation with custom response format."""
        serializer = self.get_serializer(data=request.data)
        
        try:
            serializer.is_valid(raise_exception=True)
        except serializers.ValidationError as e:
            # Provide more user-friendly error messages
            if 'non_field_errors' in e.detail:
                return Response(
                    {
                        'error': 'Invalid credentials',
                        'detail': 'Username or password is incorrect'
                    },
                    status=status.HTTP_401_UNAUTHORIZED
                )
            return Response(
                {
                    'error': 'Validation failed',
                    'detail': e.detail
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        data = serializer.validated_data
        
        # Check if user has access to any organizations
        if not data['organizations'] and not data['user']['is_superuser']:
            return Response(
                {
                    'error': 'No organization access',
                    'detail': 'User does not belong to any active organizations'
                },
                status=status.HTTP_403_FORBIDDEN
            )
        
        return Response(data, status=status.HTTP_200_OK)


class CustomTokenRefreshView(TokenRefreshView):
    """
    Custom token refresh view with organization context.
    """
    serializer_class = CustomTokenRefreshSerializer
