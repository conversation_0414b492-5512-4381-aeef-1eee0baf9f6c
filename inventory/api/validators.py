"""
Custom validators for the Inventory Management System API.

This module provides security-focused validation for API inputs
to prevent common security vulnerabilities.
"""

import re
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator
from rest_framework import serializers


class SecureTextValidator:
    """
    Validator to prevent XSS and injection attacks in text fields.
    """
    
    # Patterns that might indicate malicious content
    DANGEROUS_PATTERNS = [
        r'<script[^>]*>.*?</script>',  # Script tags
        r'javascript:',  # JavaScript URLs
        r'on\w+\s*=',  # Event handlers
        r'<iframe[^>]*>',  # Iframe tags
        r'<object[^>]*>',  # Object tags
        r'<embed[^>]*>',  # Embed tags
        r'<link[^>]*>',  # Link tags
        r'<meta[^>]*>',  # Meta tags
        r'<style[^>]*>.*?</style>',  # Style tags
        r'expression\s*\(',  # CSS expressions
        r'url\s*\(',  # CSS URLs
        r'@import',  # CSS imports
    ]
    
    def __call__(self, value):
        """Validate that the text doesn't contain dangerous patterns."""
        if not isinstance(value, str):
            return
        
        # Convert to lowercase for case-insensitive matching
        lower_value = value.lower()
        
        for pattern in self.DANGEROUS_PATTERNS:
            if re.search(pattern, lower_value, re.IGNORECASE | re.DOTALL):
                raise ValidationError(
                    f'Text contains potentially dangerous content: {pattern}',
                    code='dangerous_content'
                )


class ItemCodeValidator(RegexValidator):
    """
    Validator for item codes to ensure they are valid hexadecimal strings.
    """
    regex = r'^[0-9a-fA-F]{4,8}$'
    message = 'Item code must be 4-8 character hexadecimal string'
    code = 'invalid_item_code'


class EPCValidator(RegexValidator):
    """
    Validator for EPC codes to ensure they are valid 96-bit hex strings.
    """
    regex = r'^[0-9a-fA-F]{24}$'
    message = 'EPC must be a 24-character hexadecimal string (96 bits)'
    code = 'invalid_epc'


class OrganizationCodeValidator(RegexValidator):
    """
    Validator for organization codes.
    """
    regex = r'^[A-Z0-9_]{2,10}$'
    message = 'Organization code must be 2-10 uppercase alphanumeric characters or underscores'
    code = 'invalid_org_code'


class SafeFilenameValidator:
    """
    Validator to ensure uploaded filenames are safe.
    """
    
    DANGEROUS_EXTENSIONS = [
        '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js',
        '.jar', '.php', '.asp', '.aspx', '.jsp', '.py', '.rb', '.pl',
        '.sh', '.bash', '.ps1', '.psm1'
    ]
    
    DANGEROUS_NAMES = [
        'con', 'prn', 'aux', 'nul', 'com1', 'com2', 'com3', 'com4',
        'com5', 'com6', 'com7', 'com8', 'com9', 'lpt1', 'lpt2',
        'lpt3', 'lpt4', 'lpt5', 'lpt6', 'lpt7', 'lpt8', 'lpt9'
    ]
    
    def __call__(self, value):
        """Validate that the filename is safe."""
        if not hasattr(value, 'name'):
            return
        
        filename = value.name.lower()
        
        # Check for dangerous extensions
        for ext in self.DANGEROUS_EXTENSIONS:
            if filename.endswith(ext):
                raise ValidationError(
                    f'File extension {ext} is not allowed',
                    code='dangerous_extension'
                )
        
        # Check for dangerous filenames
        name_without_ext = filename.split('.')[0]
        if name_without_ext in self.DANGEROUS_NAMES:
            raise ValidationError(
                f'Filename {name_without_ext} is not allowed',
                code='dangerous_filename'
            )
        
        # Check for path traversal attempts
        if '..' in filename or '/' in filename or '\\' in filename:
            raise ValidationError(
                'Filename contains invalid characters',
                code='invalid_filename'
            )


class ImageValidator:
    """
    Validator for uploaded images.
    """
    
    def __init__(self, max_size=10*1024*1024, allowed_formats=None):
        self.max_size = max_size
        self.allowed_formats = allowed_formats or ['JPEG', 'PNG', 'GIF']
    
    def __call__(self, value):
        """Validate the uploaded image."""
        if not value:
            return
        
        # Check file size
        if value.size > self.max_size:
            raise ValidationError(
                f'Image size {value.size} exceeds maximum allowed size {self.max_size}',
                code='file_too_large'
            )
        
        # Validate filename
        filename_validator = SafeFilenameValidator()
        filename_validator(value)
        
        # Check if it's actually an image
        try:
            from PIL import Image
            image = Image.open(value)
            
            # Check format
            if image.format not in self.allowed_formats:
                raise ValidationError(
                    f'Image format {image.format} not allowed. Allowed formats: {self.allowed_formats}',
                    code='invalid_format'
                )
            
            # Check for potential image bombs
            if image.size[0] * image.size[1] > 50000000:  # 50 megapixels
                raise ValidationError(
                    'Image dimensions are too large',
                    code='image_too_large'
                )
            
        except Exception as e:
            raise ValidationError(
                f'Invalid image file: {str(e)}',
                code='invalid_image'
            )


class JSONFieldValidator:
    """
    Validator for JSON fields to prevent injection attacks.
    """
    
    def __init__(self, max_depth=5, max_keys=50):
        self.max_depth = max_depth
        self.max_keys = max_keys
    
    def __call__(self, value):
        """Validate JSON field content."""
        if not value:
            return
        
        # Check depth
        depth = self._get_depth(value)
        if depth > self.max_depth:
            raise ValidationError(
                f'JSON depth {depth} exceeds maximum allowed depth {self.max_depth}',
                code='json_too_deep'
            )
        
        # Check number of keys
        key_count = self._count_keys(value)
        if key_count > self.max_keys:
            raise ValidationError(
                f'JSON has {key_count} keys, exceeds maximum allowed {self.max_keys}',
                code='too_many_keys'
            )
        
        # Check for dangerous content in string values
        text_validator = SecureTextValidator()
        self._validate_strings(value, text_validator)
    
    def _get_depth(self, obj, current_depth=0):
        """Calculate the depth of a nested object."""
        if isinstance(obj, dict):
            if not obj:
                return current_depth
            return max(self._get_depth(v, current_depth + 1) for v in obj.values())
        elif isinstance(obj, list):
            if not obj:
                return current_depth
            return max(self._get_depth(item, current_depth + 1) for item in obj)
        else:
            return current_depth
    
    def _count_keys(self, obj):
        """Count the total number of keys in a nested object."""
        if isinstance(obj, dict):
            count = len(obj)
            for value in obj.values():
                count += self._count_keys(value)
            return count
        elif isinstance(obj, list):
            return sum(self._count_keys(item) for item in obj)
        else:
            return 0
    
    def _validate_strings(self, obj, validator):
        """Recursively validate all string values in the object."""
        if isinstance(obj, dict):
            for key, value in obj.items():
                if isinstance(key, str):
                    validator(key)
                self._validate_strings(value, validator)
        elif isinstance(obj, list):
            for item in obj:
                self._validate_strings(item, validator)
        elif isinstance(obj, str):
            validator(obj)


# Validator instances for common use cases
secure_text_validator = SecureTextValidator()
item_code_validator = ItemCodeValidator()
epc_validator = EPCValidator()
organization_code_validator = OrganizationCodeValidator()
safe_filename_validator = SafeFilenameValidator()
image_validator = ImageValidator()
comment_photo_validator = ImageValidator(max_size=10*1024*1024, allowed_formats=['JPEG'])
json_field_validator = JSONFieldValidator()
