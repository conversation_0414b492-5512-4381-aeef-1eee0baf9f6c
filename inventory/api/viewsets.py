"""
ViewSets for the Inventory Management System API.

This module contains all the ViewSets that provide CRUD operations
for the main models through REST API endpoints.
"""

from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q

from ..models import (
    Item,
    Organization,
    OrganizationUser,
    ItemComment,
    ManagedListValue,
)
from .serializers import (
    ItemSerializer,
    ItemListSerializer,
    OrganizationSerializer,
    ItemCommentSerializer,
    ManagedListValueSerializer,
)
from .permissions import OrganizationPermission, GuestCommentPermission
from .throttling import CommentCreateThrottle, GuestCommentThrottle
from .filters import ItemFilter, ItemCommentFilter


class OrganizationViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for Organization model.
    Provides read-only access to organizations the user belongs to.
    """
    serializer_class = OrganizationSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Return organizations the user has access to."""
        if self.request.user.is_superuser:
            return Organization.objects.all()
        
        return Organization.objects.filter(
            organizationuser__user=self.request.user
        ).distinct()


class ManagedListValueViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for ManagedListValue model.
    Provides read-only access to managed list values for the current organization.
    """
    serializer_class = ManagedListValueSerializer
    permission_classes = [IsAuthenticated, OrganizationPermission]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['list_name', 'is_active']
    search_fields = ['value']
    
    def get_queryset(self):
        """Return managed list values for the current organization."""
        return ManagedListValue.objects.filter(
            organization=self.request.organization,
            is_active=True
        ).order_by('list_name', 'value')


class ItemViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Item model.
    Provides full CRUD operations for inventory items.
    """
    permission_classes = [IsAuthenticated, OrganizationPermission]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = ItemFilter
    search_fields = ['item_name', 'item_description']
    ordering_fields = ['item_name', 'date_added', 'last_updated']
    ordering = ['-last_updated']
    
    def get_queryset(self):
        """Return items for the current organization."""
        queryset = Item.objects.filter(
            organization=self.request.organization
        ).select_related(
            'item_type', 'status', 'located_in', 'organization'
        ).prefetch_related('comments')
        
        # Filter out archived items by default
        if self.action == 'list' and not self.request.query_params.get('include_archived'):
            queryset = queryset.filter(is_archived=False)
            
        return queryset
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'list':
            return ItemListSerializer
        return ItemSerializer
    
    def perform_create(self, serializer):
        """Set organization when creating an item."""
        serializer.save(organization=self.request.organization)
    
    @action(detail=True, methods=['post'])
    def archive(self, request, pk=None):
        """Archive an item."""
        item = self.get_object()
        item.is_archived = True
        item.save()
        return Response({'status': 'archived'})
    
    @action(detail=True, methods=['post'])
    def unarchive(self, request, pk=None):
        """Unarchive an item."""
        item = self.get_object()
        item.is_archived = False
        item.save()
        return Response({'status': 'unarchived'})
    
    @action(detail=True, methods=['get'])
    def contained_items(self, request, pk=None):
        """Get items contained within this item."""
        item = self.get_object()
        contained_items = Item.objects.filter(
            located_in=item,
            is_archived=False
        ).order_by('item_name')
        
        serializer = ItemListSerializer(contained_items, many=True, context={'request': request})
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def comments(self, request, pk=None):
        """Get comments for this item."""
        item = self.get_object()
        comments = item.comments.filter(is_archived=False).order_by('-created_at')
        
        serializer = ItemCommentSerializer(comments, many=True, context={'request': request})
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def by_code(self, request):
        """Get item by item code."""
        item_code = request.query_params.get('code')
        if not item_code:
            return Response(
                {'error': 'Item code parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Convert hex string to binary for lookup
            binary_code = bytes.fromhex(item_code.upper())
            item = Item.objects.get(
                _item_code=binary_code,
                organization=request.organization
            )
            serializer = self.get_serializer(item)
            return Response(serializer.data)
        except (ValueError, Item.DoesNotExist):
            return Response(
                {'error': f'Item with code "{item_code}" not found'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['get'])
    def by_epc(self, request):
        """Get item by EPC."""
        epc = request.query_params.get('epc')
        if not epc:
            return Response(
                {'error': 'EPC parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Clean EPC data (remove spaces, ensure uppercase)
            clean_epc = epc.replace(' ', '').upper()

            # Validate EPC format (should be 24 hex characters for 96-bit EPC)
            if len(clean_epc) != 24:
                return Response(
                    {'error': 'EPC must be 24 hexadecimal characters (96 bits)'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Convert hex string to binary for lookup
            epc_binary = bytes.fromhex(clean_epc)
            item = Item.objects.get(
                _epc=epc_binary,
                organization=request.organization
            )
            serializer = self.get_serializer(item)
            return Response(serializer.data)
        except ValueError:
            return Response(
                {'error': f'Invalid EPC format: "{epc}"'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Item.DoesNotExist:
            return Response(
                {'error': f'Item with EPC "{epc}" not found'},
                status=status.HTTP_404_NOT_FOUND
            )


class ItemCommentViewSet(viewsets.ModelViewSet):
    """
    ViewSet for ItemComment model.
    Provides full CRUD operations for item comments.
    Supports guest comments for create operations.
    """
    serializer_class = ItemCommentSerializer
    permission_classes = [GuestCommentPermission]
    throttle_classes = [CommentCreateThrottle, GuestCommentThrottle]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = ItemCommentFilter
    search_fields = ['comment_text', 'guest_name']
    ordering_fields = ['created_at', 'updated_at']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """Return comments for items in the current organization."""
        # For guest users, we need to get organization from the request
        organization = getattr(self.request, 'organization', None)
        if not organization:
            # Try to get organization from query params for guest users
            org_id = self.request.query_params.get('organization_id')
            if org_id:
                try:
                    from ..models import Organization
                    organization = Organization.objects.get(id=org_id, is_active=True)
                except Organization.DoesNotExist:
                    return ItemComment.objects.none()
            else:
                return ItemComment.objects.none()

        queryset = ItemComment.objects.filter(
            item__organization=organization
        ).select_related('item', 'user', 'archived_by')

        # Filter out archived comments by default
        if self.action == 'list' and not self.request.query_params.get('include_archived'):
            queryset = queryset.filter(is_archived=False)

        return queryset
    
    def perform_create(self, serializer):
        """Handle comment creation with proper user assignment."""
        # If user is authenticated, use them; otherwise, require guest_name
        if self.request.user.is_authenticated:
            serializer.save(user=self.request.user, guest_name='')
        else:
            serializer.save(user=None)
    
    @action(detail=True, methods=['post'])
    def archive(self, request, pk=None):
        """Archive a comment."""
        comment = self.get_object()
        success = comment.archive(request.user)
        if success:
            return Response({'status': 'archived'})
        else:
            return Response(
                {'error': 'Comment is already archived'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['post'])
    def unarchive(self, request, pk=None):
        """Unarchive a comment."""
        comment = self.get_object()
        success = comment.unarchive(request.user)
        if success:
            return Response({'status': 'unarchived'})
        else:
            return Response(
                {'error': 'Comment is not archived'},
                status=status.HTTP_400_BAD_REQUEST
            )
