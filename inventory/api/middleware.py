"""
Middleware for API requests.

This module contains middleware classes that handle API-specific
functionality like organization context for JWT-authenticated requests.
"""

from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
from rest_framework_simplejwt.authentication import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tion
from rest_framework_simplejwt.exceptions import Invalid<PERSON><PERSON>, TokenError
from ..models import Organization, OrganizationUser
import logging

logger = logging.getLogger(__name__)


class APIOrganizationMiddleware(MiddlewareMixin):
    """
    Middleware to handle organization context for API requests.
    
    For API requests, the organization is determined by:
    1. X-Organization-ID header
    2. organization_id query parameter
    3. Default to user's first organization if not specified
    """
    
    def process_request(self, request):
        """Process the request to set organization context for API calls."""
        
        # Only process API requests
        if not request.path.startswith('/api/'):
            return None
        
        # Skip for certain endpoints that don't need organization context
        skip_paths = [
            '/api/v1/auth/',
            '/api/v1/schema/',
            '/api/v1/docs/',
            '/api/v1/redoc/',
        ]
        
        if any(request.path.startswith(path) for path in skip_paths):
            return None
        
        # Try to authenticate the user if JWT token is present
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            self._try_jwt_authentication(request)
        
        # If user is still not authenticated, let the view handle it
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            return None
        
        # Superusers can access any organization
        if request.user.is_superuser:
            organization_id = self._get_organization_id_from_request(request)
            if organization_id:
                try:
                    request.organization = Organization.objects.get(
                        id=organization_id,
                        is_active=True
                    )
                    return None
                except Organization.DoesNotExist:
                    pass
        
        # Get organization for regular users
        organization = self._get_user_organization(request)
        if organization:
            request.organization = organization
        else:
            # Return error if no organization access
            return JsonResponse(
                {
                    'error': 'No organization access',
                    'detail': 'User does not have access to any organization or specified organization not found'
                },
                status=403
            )
        
        return None
    
    def _try_jwt_authentication(self, request):
        """Try to authenticate using JWT token."""
        try:
            jwt_auth = JWTAuthentication()
            auth_result = jwt_auth.authenticate(request)
            if auth_result:
                request.user, request.auth = auth_result
        except (InvalidToken, TokenError):
            # Invalid token, let the view handle authentication
            pass
        except Exception as e:
            logger.warning(f"JWT authentication failed: {e}")
    
    def _get_organization_id_from_request(self, request):
        """Get organization ID from request headers or query parameters."""
        # Try header first
        org_id = request.META.get('HTTP_X_ORGANIZATION_ID')
        
        # Try query parameter
        if not org_id:
            org_id = request.GET.get('organization_id')
        
        # Try POST data for non-GET requests
        if not org_id and hasattr(request, 'data') and request.data:
            org_id = request.data.get('organization_id')
        
        if org_id:
            try:
                return int(org_id)
            except (ValueError, TypeError):
                return None
        
        return None
    
    def _get_user_organization(self, request):
        """Get the appropriate organization for the user."""
        # Get requested organization ID
        requested_org_id = self._get_organization_id_from_request(request)
        
        # Get user's organizations
        user_orgs = OrganizationUser.objects.filter(
            user=request.user
        ).select_related('organization').filter(
            organization__is_active=True
        )
        
        if requested_org_id:
            # Check if user has access to the requested organization
            try:
                org_user = user_orgs.get(organization_id=requested_org_id)
                return org_user.organization
            except OrganizationUser.DoesNotExist:
                return None
        else:
            # Return the first organization the user has access to
            first_org_user = user_orgs.first()
            if first_org_user:
                return first_org_user.organization
        
        return None


class APIErrorHandlingMiddleware(MiddlewareMixin):
    """
    Middleware to handle API errors consistently.
    """
    
    def process_exception(self, request, exception):
        """Handle exceptions for API requests."""
        
        # Only handle API requests
        if not request.path.startswith('/api/'):
            return None
        
        logger.error(f"API Exception: {exception}", exc_info=True)
        
        # Return JSON error response
        return JsonResponse(
            {
                'error': 'Internal server error',
                'detail': str(exception) if hasattr(exception, 'detail') else 'An unexpected error occurred'
            },
            status=500
        )
