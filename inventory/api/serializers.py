"""
Serializers for the Inventory Management System API.

This module contains all the serializers for converting model instances
to JSON and handling API request/response data.
"""

from rest_framework import serializers
from django.contrib.auth.models import User
from django.core.files.base import ContentFile
import base64
import uuid

from ..models import (
    Item,
    Organization,
    OrganizationUser,
    ItemComment,
    ManagedListValue,
)
from .validators import (
    secure_text_validator,
    item_code_validator,
    comment_photo_validator,
    json_field_validator,
)


class Base64ImageField(serializers.ImageField):
    """
    Custom field to handle base64 encoded images from mobile apps.
    """
    def to_internal_value(self, data):
        if isinstance(data, str) and data.startswith('data:image'):
            # Extract format and base64 data
            format, imgstr = data.split(';base64,')
            ext = format.split('/')[-1]
            
            # Generate unique filename
            filename = f"{uuid.uuid4()}.{ext}"
            
            # Decode base64 data
            data = ContentFile(base64.b64decode(imgstr), name=filename)
        
        return super().to_internal_value(data)


class UserSerializer(serializers.ModelSerializer):
    """Serializer for User model."""
    
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'is_active']
        read_only_fields = ['id', 'username']


class OrganizationSerializer(serializers.ModelSerializer):
    """Serializer for Organization model."""
    
    class Meta:
        model = Organization
        fields = [
            'id', 'name', 'code', 'is_active', 'rfid_prefix',
            'logo', 'primary_color', 'secondary_color'
        ]
        read_only_fields = ['id', 'code']


class OrganizationUserSerializer(serializers.ModelSerializer):
    """Serializer for OrganizationUser model."""
    user = UserSerializer(read_only=True)
    organization = OrganizationSerializer(read_only=True)
    
    class Meta:
        model = OrganizationUser
        fields = ['user', 'organization', 'is_admin', 'can_edit', 'can_add']


class ManagedListValueSerializer(serializers.ModelSerializer):
    """Serializer for ManagedListValue model."""

    class Meta:
        model = ManagedListValue
        fields = ['id', 'list_name', 'value', 'is_active']
        read_only_fields = ['id']


class ItemCommentSerializer(serializers.ModelSerializer):
    """Serializer for ItemComment model."""
    commenter_name = serializers.ReadOnlyField()
    is_guest_comment = serializers.ReadOnlyField()
    photo = Base64ImageField(required=False, allow_null=True, validators=[comment_photo_validator])
    comment_text = serializers.CharField(validators=[secure_text_validator])
    guest_name = serializers.CharField(required=False, allow_blank=True, validators=[secure_text_validator])
    
    class Meta:
        model = ItemComment
        fields = [
            'id', 'item', 'comment_text', 'reason', 'photo',
            'user', 'guest_name', 'commenter_name', 'is_guest_comment',
            'created_at', 'updated_at', 'is_archived', 'archived_at', 'archived_by'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'commenter_name', 'is_guest_comment',
            'is_archived', 'archived_at', 'archived_by'
        ]
    
    def validate(self, data):
        """Ensure either user or guest_name is provided."""
        user = data.get('user') or self.context['request'].user if self.context.get('request') else None
        guest_name = data.get('guest_name')
        
        if not user or not user.is_authenticated:
            if not guest_name:
                raise serializers.ValidationError(
                    "Either authenticated user or guest_name must be provided"
                )
        else:
            # Clear guest_name if user is authenticated
            data['guest_name'] = ''
            data['user'] = user
            
        return data


class ItemSerializer(serializers.ModelSerializer):
    """Serializer for Item model."""
    item_code = serializers.ReadOnlyField()
    epc_hex = serializers.ReadOnlyField()
    contained_item_count = serializers.ReadOnlyField()
    primary_image = serializers.SerializerMethodField()
    image = Base64ImageField(required=False, allow_null=True)
    item_name = serializers.CharField(validators=[secure_text_validator])
    item_description = serializers.CharField(required=False, allow_blank=True, validators=[secure_text_validator])
    custom_fields = serializers.JSONField(required=False, validators=[json_field_validator])
    
    # Related fields
    item_type = ManagedListValueSerializer(read_only=True)
    status = ManagedListValueSerializer(read_only=True)
    organization = OrganizationSerializer(read_only=True)
    located_in = serializers.SerializerMethodField()
    
    # Write-only fields for creating/updating relationships
    item_type_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    status_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    located_in_code = serializers.CharField(write_only=True, required=False, allow_null=True, allow_blank=True)
    
    class Meta:
        model = Item
        fields = [
            'id', 'item_code', 'epc_hex', 'item_name', 'item_description',
            'item_type', 'item_type_id', 'status', 'status_id',
            'located_in', 'located_in_code', 'organization',
            'image', 'image_caption', 'primary_image',
            'custom_fields', 'date_added', 'last_updated',
            'is_archived', 'contained_item_count'
        ]
        read_only_fields = [
            'id', 'item_code', 'epc_hex', 'organization', 'date_added',
            'last_updated', 'contained_item_count', 'primary_image'
        ]
    
    def get_primary_image(self, obj):
        """Get the primary image URL."""
        if obj.image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.image.url)
        return None
    
    def get_located_in(self, obj):
        """Get the located_in item details."""
        if obj.located_in:
            return {
                'id': obj.located_in.id,
                'item_code': obj.located_in.item_code,
                'item_name': obj.located_in.item_name
            }
        return None
    
    def validate_located_in_code(self, value):
        """Validate and convert located_in_code to Item instance."""
        if not value:
            return None
            
        try:
            # Convert hex string to binary for lookup
            binary_code = bytes.fromhex(value.upper())
            item = Item.objects.get(_item_code=binary_code)
            return item
        except (ValueError, Item.DoesNotExist):
            raise serializers.ValidationError(f"Item with code '{value}' not found")
    
    def create(self, validated_data):
        """Create a new item with proper organization assignment."""
        # Remove write-only fields and handle them separately
        item_type_id = validated_data.pop('item_type_id', None)
        status_id = validated_data.pop('status_id', None)
        located_in_item = validated_data.pop('located_in_code', None)
        
        # Set organization from request context
        request = self.context.get('request')
        if request and hasattr(request, 'organization'):
            validated_data['organization'] = request.organization
        
        # Create the item
        item = Item.objects.create(**validated_data)
        
        # Set relationships
        if item_type_id:
            try:
                item.item_type_id = item_type_id
            except:
                pass
                
        if status_id:
            try:
                item.status_id = status_id
            except:
                pass
                
        if located_in_item:
            item.located_in = located_in_item
            
        item.save()
        return item
    
    def update(self, instance, validated_data):
        """Update an item with proper relationship handling."""
        # Handle write-only fields
        item_type_id = validated_data.pop('item_type_id', None)
        status_id = validated_data.pop('status_id', None)
        located_in_item = validated_data.pop('located_in_code', None)
        
        # Update basic fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        # Update relationships
        if item_type_id is not None:
            instance.item_type_id = item_type_id
            
        if status_id is not None:
            instance.status_id = status_id
            
        if located_in_item is not None:
            instance.located_in = located_in_item
        
        instance.save()
        return instance


class ItemListSerializer(ItemSerializer):
    """Lightweight serializer for item lists."""
    
    class Meta(ItemSerializer.Meta):
        fields = [
            'id', 'item_code', 'item_name', 'item_description',
            'item_type', 'status', 'located_in', 'primary_image',
            'date_added', 'last_updated', 'is_archived', 'contained_item_count'
        ]
