"""
API Views for the Inventory Management System.

This module contains specialized API views for mobile-specific features,
authentication, and barcode generation.
"""

from rest_framework import generics, status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth.models import User
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.db import transaction
import io

from ..models import Item, Organization, OrganizationUser
from ..utils.barcode_generator import generate_epc_barcode, BarcodeFormat
from .serializers import (
    UserSerializer,
    OrganizationSerializer,
    ItemSerializer,
    ItemListSerializer,
)
from .permissions import OrganizationPermission
from .throttling import (
    MobileScanThrottle,
    MobileBulkThrottle,
    BarcodeGenerationThrottle,
)


class CurrentUserView(generics.RetrieveUpdateAPIView):
    """
    Get and update current user information.
    """
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]
    
    def get_object(self):
        return self.request.user


class UserOrganizationsView(generics.ListAPIView):
    """
    Get organizations the current user belongs to.
    """
    serializer_class = OrganizationSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        if self.request.user.is_superuser:
            return Organization.objects.filter(is_active=True)
        
        return Organization.objects.filter(
            organizationuser__user=self.request.user,
            is_active=True
        ).distinct()


class ScanItemView(APIView):
    """
    Mobile-optimized endpoint for scanning items by code or EPC.
    Supports both barcode scanning and RFID scanning.
    """
    permission_classes = [IsAuthenticated, OrganizationPermission]
    throttle_classes = [MobileScanThrottle]
    
    def post(self, request):
        """
        Scan an item by code or EPC.
        
        Expected payload:
        {
            "scan_data": "string",  # The scanned data (hex code or EPC)
            "scan_type": "code|epc"  # Type of scan
        }
        """
        scan_data = request.data.get('scan_data', '').strip()
        scan_type = request.data.get('scan_type', 'code')
        
        if not scan_data:
            return Response(
                {'error': 'scan_data is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            if scan_type == 'epc':
                # Handle EPC scan
                item = self._find_item_by_epc(scan_data, request.organization)
            else:
                # Handle code scan
                item = self._find_item_by_code(scan_data, request.organization)
            
            if item:
                serializer = ItemSerializer(item, context={'request': request})
                return Response({
                    'found': True,
                    'item': serializer.data
                })
            else:
                return Response({
                    'found': False,
                    'scan_data': scan_data,
                    'scan_type': scan_type,
                    'message': f'No item found for {scan_type}: {scan_data}'
                })
                
        except Exception as e:
            return Response(
                {'error': f'Scan failed: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    def _find_item_by_code(self, code, organization):
        """Find item by hex code."""
        try:
            binary_code = bytes.fromhex(code.upper())
            return Item.objects.get(
                _item_code=binary_code,
                organization=organization,
                is_archived=False
            )
        except (ValueError, Item.DoesNotExist):
            return None
    
    def _find_item_by_epc(self, epc, organization):
        """Find item by EPC."""
        try:
            # Clean EPC data (remove spaces, ensure uppercase)
            clean_epc = epc.replace(' ', '').upper()

            # Validate EPC format (should be 24 hex characters for 96-bit EPC)
            if len(clean_epc) != 24:
                return None

            # Convert hex string to binary for lookup
            epc_binary = bytes.fromhex(clean_epc)
            return Item.objects.get(
                _epc=epc_binary,
                organization=organization,
                is_archived=False
            )
        except (ValueError, Item.DoesNotExist):
            return None


class BulkCreateItemsView(APIView):
    """
    Bulk create multiple items for mobile efficiency.
    """
    permission_classes = [IsAuthenticated, OrganizationPermission]
    throttle_classes = [MobileBulkThrottle]
    
    def post(self, request):
        """
        Create multiple items in a single request.
        
        Expected payload:
        {
            "items": [
                {
                    "item_name": "string",
                    "item_description": "string",
                    ...
                }
            ]
        }
        """
        items_data = request.data.get('items', [])
        
        if not items_data or not isinstance(items_data, list):
            return Response(
                {'error': 'items array is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if len(items_data) > 50:  # Limit bulk operations
            return Response(
                {'error': 'Maximum 50 items per bulk operation'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        created_items = []
        errors = []
        
        with transaction.atomic():
            for i, item_data in enumerate(items_data):
                try:
                    serializer = ItemSerializer(
                        data=item_data,
                        context={'request': request}
                    )
                    if serializer.is_valid():
                        item = serializer.save(organization=request.organization)
                        created_items.append(serializer.data)
                    else:
                        errors.append({
                            'index': i,
                            'errors': serializer.errors
                        })
                except Exception as e:
                    errors.append({
                        'index': i,
                        'errors': {'general': [str(e)]}
                    })
        
        return Response({
            'created_count': len(created_items),
            'error_count': len(errors),
            'created_items': created_items,
            'errors': errors
        }, status=status.HTTP_201_CREATED if created_items else status.HTTP_400_BAD_REQUEST)


class ItemBarcodeView(APIView):
    """
    Generate barcode image for an item.
    """
    permission_classes = [IsAuthenticated, OrganizationPermission]
    throttle_classes = [BarcodeGenerationThrottle]
    
    def get(self, request, item_code):
        """
        Generate and return barcode image for an item.
        
        Query parameters:
        - format: Barcode format (datamatrix, qr, code128, auto)
        - size: Image size in pixels (default: 200)
        """
        try:
            # Find the item
            binary_code = bytes.fromhex(item_code.upper())
            item = get_object_or_404(
                Item,
                _item_code=binary_code,
                organization=request.organization
            )
            
            # Get parameters
            format_type = request.query_params.get('format', 'auto')
            size = int(request.query_params.get('size', 200))
            
            # Generate barcode using EPC if available, otherwise use item code
            data = item.epc_hex if item.epc_hex else item.item_code
            
            if format_type == 'auto':
                format_type = None  # Let the generator choose
            
            # Generate barcode
            buffer, used_format, metadata = generate_epc_barcode(
                data,
                format_type=format_type,
                size=size
            )
            
            # Determine content type
            content_type = 'image/png'
            
            # Create response
            response = HttpResponse(buffer.getvalue(), content_type=content_type)
            response['Content-Disposition'] = f'inline; filename="item_{item_code}_barcode.png"'
            response['Cache-Control'] = 'max-age=3600'  # Cache for 1 hour
            
            return response
            
        except ValueError:
            return Response(
                {'error': 'Invalid item code format'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {'error': f'Barcode generation failed: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ItemQRCodeView(APIView):
    """
    Generate QR code for an item with URL.
    """
    permission_classes = [IsAuthenticated, OrganizationPermission]
    throttle_classes = [BarcodeGenerationThrottle]
    
    def get(self, request, item_code):
        """
        Generate QR code containing the item's URL.
        """
        try:
            # Find the item
            binary_code = bytes.fromhex(item_code.upper())
            item = get_object_or_404(
                Item,
                _item_code=binary_code,
                organization=request.organization
            )
            
            # Get parameters
            size = int(request.query_params.get('size', 200))
            
            # Create URL for the item
            item_url = request.build_absolute_uri(f'/{item.item_code}')
            
            # Generate QR code
            from ..utils.barcode_generator import generate_qr_code
            buffer = generate_qr_code(item_url, size=size)
            
            # Create response
            response = HttpResponse(buffer.getvalue(), content_type='image/png')
            response['Content-Disposition'] = f'inline; filename="item_{item_code}_qr.png"'
            response['Cache-Control'] = 'max-age=3600'  # Cache for 1 hour
            
            return response
            
        except ValueError:
            return Response(
                {'error': 'Invalid item code format'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {'error': f'QR code generation failed: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
