"""
URL configuration for the Inventory Management System API.

This module defines the API endpoints with versioning support.
All API endpoints are prefixed with /api/v1/
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from rest_framework_simplejwt.views import TokenVerifyView
from drf_spectacular.views import (
    SpectacularAPIView,
    SpectacularSwaggerView,
    SpectacularRedocView,
)

from . import views
from .auth_views import (
    CustomTokenObtainPairView,
    CustomTokenRefreshView,
)
from .viewsets import (
    ItemViewSet,
    OrganizationViewSet,
    ItemCommentViewSet,
    ManagedListValueViewSet,
)

# Create router for ViewSets
router = DefaultRouter()
router.register(r'items', ItemViewSet, basename='item')
router.register(r'organizations', OrganizationViewSet, basename='organization')
router.register(r'comments', ItemCommentViewSet, basename='comment')
router.register(r'managed-lists', ManagedListValueViewSet, basename='managedlistvalue')

app_name = 'api'

urlpatterns = [
    # Authentication endpoints
    path('auth/token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('auth/token/refresh/', CustomTokenRefreshView.as_view(), name='token_refresh'),
    path('auth/token/verify/', TokenVerifyView.as_view(), name='token_verify'),
    path('auth/user/', views.CurrentUserView.as_view(), name='current_user'),
    path('auth/organizations/', views.UserOrganizationsView.as_view(), name='user_organizations'),
    
    # API Documentation
    path('schema/', SpectacularAPIView.as_view(), name='schema'),
    path('docs/', SpectacularSwaggerView.as_view(url_name='api:schema'), name='swagger-ui'),
    path('redoc/', SpectacularRedocView.as_view(url_name='api:schema'), name='redoc'),
    
    # Mobile-specific endpoints
    path('mobile/scan/', views.ScanItemView.as_view(), name='scan_item'),
    path('mobile/bulk-create/', views.BulkCreateItemsView.as_view(), name='bulk_create_items'),
    
    # Barcode generation endpoints
    path('items/<str:item_code>/barcode/', views.ItemBarcodeView.as_view(), name='item_barcode'),
    path('items/<str:item_code>/qr/', views.ItemQRCodeView.as_view(), name='item_qr'),
    
    # Include router URLs
    path('', include(router.urls)),
]
