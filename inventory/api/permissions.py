"""
Custom permissions for the Inventory Management System API.

This module contains permission classes that handle organization-based
access control and user permissions within organizations.
"""

from rest_framework import permissions
from ..models import OrganizationUser


class OrganizationPermission(permissions.BasePermission):
    """
    Custom permission to check organization access and user permissions.
    
    This permission class ensures that:
    1. User has access to the current organization
    2. User has appropriate permissions for the requested action
    """
    
    def has_permission(self, request, view):
        """
        Check if user has permission to access the view.
        """
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Superusers have access to everything
        if request.user.is_superuser:
            return True
        
        # Check if organization is set in request
        organization = getattr(request, 'organization', None)
        if not organization:
            return False
        
        try:
            org_user = OrganizationUser.objects.get(
                user=request.user,
                organization=organization
            )
        except OrganizationUser.DoesNotExist:
            return False
        
        # Check action-specific permissions
        if view.action in ['create', 'bulk_create']:
            return org_user.can_add
        elif view.action in ['update', 'partial_update', 'archive', 'unarchive']:
            return org_user.can_edit
        elif view.action in ['destroy']:
            return org_user.is_admin  # Only admins can delete
        else:
            # Read operations are allowed for all organization members
            return True
    
    def has_object_permission(self, request, view, obj):
        """
        Check if user has permission to access the specific object.
        """
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Superusers have access to everything
        if request.user.is_superuser:
            return True
        
        # Check if the object belongs to the user's current organization
        organization = getattr(request, 'organization', None)
        if not organization:
            return False
        
        # Get the organization from the object
        obj_organization = None
        if hasattr(obj, 'organization'):
            obj_organization = obj.organization
        elif hasattr(obj, 'item') and hasattr(obj.item, 'organization'):
            # For comments, check the item's organization
            obj_organization = obj.item.organization
        
        if obj_organization != organization:
            return False
        
        # Check user permissions within the organization
        try:
            org_user = OrganizationUser.objects.get(
                user=request.user,
                organization=organization
            )
        except OrganizationUser.DoesNotExist:
            return False
        
        # Check action-specific permissions
        if view.action in ['update', 'partial_update', 'archive', 'unarchive']:
            # For comments, users can only edit their own comments
            if hasattr(obj, 'user') and obj.user == request.user:
                return True
            return org_user.can_edit
        elif view.action in ['destroy']:
            return org_user.is_admin
        else:
            # Read operations are allowed
            return True


class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object to edit it.
    """
    
    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed for any request
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Write permissions are only allowed to the owner of the object
        return obj.user == request.user


class GuestCommentPermission(permissions.BasePermission):
    """
    Permission class for guest comments.
    Allows unauthenticated users to create comments but not modify them.
    """
    
    def has_permission(self, request, view):
        # Allow read access to everyone
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Allow create for everyone (authenticated and guest)
        if view.action == 'create':
            return True
        
        # All other actions require authentication
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        # Read permissions for everyone
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Guest comments cannot be edited after creation
        if obj.is_guest_comment:
            return False
        
        # Authenticated users can edit their own comments
        return obj.user == request.user
