from django.test import TestCase, Client
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.contrib.auth.models import User
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import Organization, Item, ManagedListValue, OrganizationUser, ItemImage, ItemComment
from .templatetags.inventory_filters import location_breadcrumb
import secrets


class OrganizationModelTest(TestCase):
    """Test cases for the Organization model"""

    def setUp(self):
        """Set up test data"""
        self.valid_org_data = {
            'name': 'Test Organization',
            'code': 'TEST001',
            'is_active': True,
            'primary_color': '#007bff',
            'secondary_color': '#6c757d'
        }

    def test_create_organization_with_valid_data(self):
        """Test creating an organization with valid data"""
        org = Organization.objects.create(**self.valid_org_data)
        self.assertEqual(org.name, 'Test Organization')
        self.assertEqual(org.code, 'TEST001')

        self.assertTrue(org.is_active)
        self.assertEqual(org.primary_color, '#007bff')
        self.assertEqual(org.secondary_color, '#6c757d')

    def test_organization_str_representation(self):
        """Test the string representation of Organization"""
        org = Organization.objects.create(**self.valid_org_data)
        self.assertEqual(str(org), 'Test Organization')





    def test_unique_constraints(self):
        """Test unique constraints on code"""
        # Create first organization
        org1 = Organization.objects.create(**self.valid_org_data)

        # Test duplicate code
        org_data_dup_code = self.valid_org_data.copy()
        with self.assertRaises(IntegrityError):
            Organization.objects.create(**org_data_dup_code)

    def test_default_values(self):
        """Test default values for optional fields"""
        minimal_data = {
            'name': 'Minimal Org',
            'code': 'MIN001'
        }
        org = Organization.objects.create(**minimal_data)

        self.assertTrue(org.is_active)  # Default should be True
        self.assertEqual(org.primary_color, '#007bff')  # Default value
        self.assertEqual(org.secondary_color, '#6c757d')  # Default value
        self.assertFalse(org.logo)  # Should be falsy (empty ImageFieldFile)

    def test_logo_upload(self):
        """Test logo image upload"""
        # Create a simple test image file
        image_content = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
        uploaded_file = SimpleUploadedFile("test_logo.png", image_content, content_type="image/png")

        org_data = self.valid_org_data.copy()
        org_data['logo'] = uploaded_file

        org = Organization.objects.create(**org_data)
        self.assertTrue(org.logo)
        self.assertTrue(org.logo.name.startswith('org_logos/'))

    def test_color_field_validation(self):
        """Test color field format validation"""
        # Valid color formats should work
        valid_colors = ['#000000', '#ffffff', '#123abc', '#ABC123']

        for i, color in enumerate(valid_colors):
            org_data = self.valid_org_data.copy()
            org_data['code'] = f'COLOR{i:03d}'

            org_data['primary_color'] = color

            org = Organization.objects.create(**org_data)
            self.assertEqual(org.primary_color, color)


class ItemModelTest(TestCase):
    """Test cases for the Item model"""

    def setUp(self):
        """Set up test data"""
        # Create test organization
        self.organization = Organization.objects.create(
            name='Test Org',
            code='TEST001',
            is_active=True,
            rfid_prefix=1  # Ensure test organization has a valid rfid_prefix
        )

        # Create test managed list values
        self.item_type = ManagedListValue.objects.create(
            organization=self.organization,
            list_name='ItemTypes',
            value='Electronics',
            is_active=True
        )

        self.item_status = ManagedListValue.objects.create(
            organization=self.organization,
            list_name='ItemStatuses',
            value='Available',
            is_active=True
        )

        self.valid_item_data = {
            'item_name': 'Test Item',
            'item_description': 'A test item for testing',
            'organization': self.organization,
            'item_type': self.item_type,
            'status': self.item_status,
            'is_archived': False
        }

    def test_create_item_with_valid_data(self):
        """Test creating an item with valid data"""
        item = Item.objects.create(**self.valid_item_data)

        self.assertEqual(item.item_name, 'Test Item')
        self.assertEqual(item.item_description, 'A test item for testing')
        self.assertEqual(item.organization, self.organization)
        self.assertEqual(item.item_type, self.item_type)
        self.assertEqual(item.status, self.item_status)
        self.assertFalse(item.is_archived)

        # Check that item_code was automatically generated
        self.assertIsNotNone(item.item_code)
        self.assertEqual(len(item.item_code), 6)  # 3 bytes = 6 hex chars



    def test_item_str_representation(self):
        """Test the string representation of Item"""
        item = Item.objects.create(**self.valid_item_data)
        expected_str = f"Test Item ({item.item_code})"
        self.assertEqual(str(item), expected_str)

    def test_item_code_generation_uniqueness(self):
        """Test that item codes are unique"""
        items = []
        for i in range(10):
            item_data = self.valid_item_data.copy()
            item_data['item_name'] = f'Test Item {i}'
            items.append(Item.objects.create(**item_data))

        # Check all item codes are unique
        item_codes = [item.item_code for item in items]
        self.assertEqual(len(item_codes), len(set(item_codes)))

    def test_item_code_property_getter_setter(self):
        """Test item_code property getter and setter"""
        item = Item.objects.create(**self.valid_item_data)

        # Test getter returns hex string
        original_code = item.item_code
        self.assertIsInstance(original_code, str)
        self.assertEqual(len(original_code), 6)

        # Test setter with hex string
        new_code = 'abcdef'
        item.item_code = new_code
        self.assertEqual(item.item_code, new_code)

        # Test setter with bytes
        item.item_code = bytes.fromhex('123456')
        self.assertEqual(item.item_code, '123456')



    def test_location_hierarchy_validation_self_reference(self):
        """Test validation prevents self-reference in location"""
        item = Item.objects.create(**self.valid_item_data)

        # Try to set item as its own location
        with self.assertRaises(ValidationError) as context:
            item.validate_location_hierarchy(item)

        self.assertIn("cannot be located inside itself", str(context.exception))

    def test_location_hierarchy_validation_circular_reference(self):
        """Test validation prevents circular references"""
        # Create three items
        item1 = Item.objects.create(item_name='Item 1', organization=self.organization)
        item2 = Item.objects.create(item_name='Item 2', organization=self.organization)
        item3 = Item.objects.create(item_name='Item 3', organization=self.organization)

        # Set up hierarchy: item1 -> item2 -> item3
        item2.located_in = item1
        item2.save()
        item3.located_in = item2
        item3.save()

        # Try to create circular reference: item1 -> item3 (which would create cycle)
        with self.assertRaises(ValidationError) as context:
            item1.validate_location_hierarchy(item3)

        self.assertIn("circular reference", str(context.exception))

    def test_location_hierarchy_valid_cases(self):
        """Test valid location hierarchy cases"""
        container = Item.objects.create(item_name='Container', organization=self.organization)
        item = Item.objects.create(item_name='Item', organization=self.organization)

        # Valid: item inside container
        self.assertTrue(item.validate_location_hierarchy(container))

        # Valid: no location (None)
        self.assertTrue(item.validate_location_hierarchy(None))

    def test_contained_item_count(self):
        """Test contained_item_count property"""
        container = Item.objects.create(item_name='Container', organization=self.organization)

        # Initially no items
        self.assertEqual(container.contained_item_count, 0)

        # Add some items
        for i in range(3):
            Item.objects.create(
                item_name=f'Item {i}',
                organization=self.organization,
                located_in=container
            )

        self.assertEqual(container.contained_item_count, 3)

        # Archived items shouldn't count
        archived_item = Item.objects.create(
            item_name='Archived Item',
            organization=self.organization,
            located_in=container,
            is_archived=True
        )

        self.assertEqual(container.contained_item_count, 3)  # Still 3, not 4

    def test_get_absolute_url(self):
        """Test get_absolute_url method"""
        item = Item.objects.create(**self.valid_item_data)
        expected_url = f'/inventory/item/{item.item_code}/'
        # Note: This assumes the URL pattern exists - adjust if different
        self.assertEqual(item.get_absolute_url(), expected_url)

    def test_image_properties(self):
        """Test image-related properties"""
        item = Item.objects.create(**self.valid_item_data)

        # Initially no image
        self.assertFalse(item.has_images)
        self.assertIsNone(item.primary_image)
        self.assertIsNone(item.image_url)

        # Add image
        image_content = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
        uploaded_file = SimpleUploadedFile("test_item.png", image_content, content_type="image/png")

        item.image = uploaded_file
        item.save()

        self.assertTrue(item.has_images)
        self.assertIsNotNone(item.primary_image)
        self.assertIsNotNone(item.image_url)

    def test_epc_generation_with_valid_data(self):
        """Test EPC generation with valid organization and item code"""
        # Set a specific rfid_prefix for predictable testing
        self.organization.rfid_prefix = 123  # Binary: 001111011
        self.organization.save()

        item = Item.objects.create(**self.valid_item_data)

        # Manually set item code for predictable testing
        item.item_code = 'abcdef'  # 3 bytes: 0xabcdef

        epc_int = item.generate_epc()
        self.assertIsNotNone(epc_int)

        # Verify bit structure: [Org Prefix (reversed): 12 bits][Padding: 60 bits][Item Code: 24 bits]
        # Original prefix 123 = 0b001111011 (9 bits) = 0b000001111011 (12 bits)
        # Reversed: 0b110111100000 = 3552
        expected_org_prefix_original = 123
        expected_org_prefix_reversed = item._reverse_bits(123, 12)  # Should be 3552
        expected_item_code = 0xabcdef
        expected_padding = 0

        # Extract components from generated EPC
        extracted_item_code = epc_int & 0xFFFFFF  # Last 24 bits
        extracted_padding = (epc_int >> 24) & 0xFFFFFFFFFFFFFFF  # Next 60 bits
        extracted_org_prefix_reversed = (epc_int >> 84) & 0xFFF  # First 12 bits

        # Verify the reversed prefix is stored, not the original
        self.assertEqual(extracted_org_prefix_reversed, expected_org_prefix_reversed)
        self.assertNotEqual(extracted_org_prefix_reversed, expected_org_prefix_original)
        self.assertEqual(extracted_padding, expected_padding)
        self.assertEqual(extracted_item_code, expected_item_code)

    def test_bit_reversal_function(self):
        """Test the bit reversal utility function"""
        item = Item.objects.create(**self.valid_item_data)

        # Test bit reversal with known values
        test_cases = [
            (0b000000000001, 12, 0b100000000000),  # 1 -> 2048
            (0b000001111011, 12, 0b110111100000),  # 123 -> 3552
            (0b111111111111, 12, 0b111111111111),  # 4095 -> 4095 (palindrome)
            (0b000000000000, 12, 0b000000000000),  # 0 -> 0
        ]

        for original, bit_count, expected in test_cases:
            result = item._reverse_bits(original, bit_count)
            self.assertEqual(result, expected,
                f"Reversing {original:012b} should give {expected:012b}, got {result:012b}")

    def test_epc_property_hex_format(self):
        """Test EPC property returns properly formatted hex string"""
        self.organization.rfid_prefix = 255  # 0xFF = 0b000011111111
        self.organization.save()

        item = Item.objects.create(**self.valid_item_data)
        item.item_code = '123456'

        epc_hex = item.epc
        self.assertIsNotNone(epc_hex)
        self.assertEqual(len(epc_hex), 24)  # 96 bits = 24 hex chars
        self.assertTrue(all(c in '0123456789ABCDEF' for c in epc_hex))

        # Calculate expected reversed prefix: 255 = 0b000011111111 -> 0b111111000000 = 4032 = 0xFC0
        expected_reversed_prefix = item._reverse_bits(255, 12)
        expected_hex_start = f"{expected_reversed_prefix:03X}"
        self.assertTrue(epc_hex.startswith(expected_hex_start))

    def test_epc_formatted_property(self):
        """Test EPC formatted property adds spaces for readability"""
        self.organization.rfid_prefix = 1
        self.organization.save()

        item = Item.objects.create(**self.valid_item_data)

        epc_formatted = item.epc_formatted
        self.assertIsNotNone(epc_formatted)

        # Should have spaces every 4 characters
        parts = epc_formatted.split(' ')
        self.assertEqual(len(parts), 6)  # 24 chars / 4 = 6 parts
        for part in parts:
            self.assertEqual(len(part), 4)

    def test_epc_generation_without_organization(self):
        """Test EPC generation fails gracefully without organization"""
        item_data = self.valid_item_data.copy()
        item_data['organization'] = None

        # This should fail due to model constraints, but let's test the EPC method directly
        item = Item(**item_data)
        item._item_code = bytes.fromhex('123456')

        epc_int = item.generate_epc()
        self.assertIsNone(epc_int)

        epc_hex = item.epc
        self.assertIsNone(epc_hex)

        epc_formatted = item.epc_formatted
        self.assertIsNone(epc_formatted)

    def test_epc_generation_without_rfid_prefix(self):
        """Test EPC generation fails gracefully without rfid_prefix"""
        # Create organization without rfid_prefix
        org_without_prefix = Organization(
            name='No Prefix Org',
            code='NOPREFIX',
            rfid_prefix=None
        )

        item_data = self.valid_item_data.copy()
        item_data['organization'] = org_without_prefix

        item = Item(**item_data)
        item._item_code = bytes.fromhex('123456')

        epc_int = item.generate_epc()
        self.assertIsNone(epc_int)

    def test_epc_generation_without_item_code(self):
        """Test EPC generation fails gracefully without item code"""
        self.organization.rfid_prefix = 100
        self.organization.save()

        item_data = self.valid_item_data.copy()
        item = Item(**item_data)
        item._item_code = None

        epc_int = item.generate_epc()
        self.assertIsNone(epc_int)

    def test_epc_bit_boundaries(self):
        """Test EPC generation with boundary values"""
        # Test maximum values for each component
        self.organization.rfid_prefix = 4095  # Maximum 12-bit value (0b111111111111)
        self.organization.save()

        item = Item.objects.create(**self.valid_item_data)
        item.item_code = 'ffffff'  # Maximum 24-bit value

        epc_int = item.generate_epc()
        self.assertIsNotNone(epc_int)

        # Verify components are properly masked
        extracted_item_code = epc_int & 0xFFFFFF
        extracted_org_prefix_reversed = (epc_int >> 84) & 0xFFF

        # 4095 = 0b111111111111 reversed is still 0b111111111111 = 4095 (palindrome)
        expected_reversed_prefix = item._reverse_bits(4095, 12)
        self.assertEqual(extracted_org_prefix_reversed, expected_reversed_prefix)
        self.assertEqual(extracted_item_code, 0xFFFFFF)

    def test_epc_generation_with_oversized_values(self):
        """Test EPC generation properly masks oversized values"""
        # Set rfid_prefix larger than 12 bits (should be masked)
        self.organization.rfid_prefix = 8191  # 13 bits, should be masked to 12 bits
        self.organization.save()

        item = Item.objects.create(**self.valid_item_data)
        # Item code is already limited to 3 bytes by the model

        epc_int = item.generate_epc()
        self.assertIsNotNone(epc_int)

        # Verify organization prefix is properly masked to 12 bits
        extracted_org_prefix = (epc_int >> 84) & 0xFFF
        expected_masked_prefix = 8191 & 0xFFF  # Should be 4095 (0xFFF)
        self.assertEqual(extracted_org_prefix, expected_masked_prefix)

    def test_epc_uniqueness_across_organizations(self):
        """Test that EPCs are unique across different organizations"""
        # Create second organization
        org2 = Organization.objects.create(
            name='Test Org 2',
            code='TEST002',
            rfid_prefix=456
        )

        self.organization.rfid_prefix = 123
        self.organization.save()

        # Create items with same item code in different organizations
        item1 = Item.objects.create(
            item_name='Item 1',
            organization=self.organization
        )
        item2 = Item.objects.create(
            item_name='Item 2',
            organization=org2
        )

        # Set same item code for both
        item1.item_code = 'abcdef'
        item2.item_code = 'abcdef'

        epc1 = item1.epc
        epc2 = item2.epc

        self.assertIsNotNone(epc1)
        self.assertIsNotNone(epc2)
        self.assertNotEqual(epc1, epc2)  # Should be different due to different org prefixes

    def test_epc_expansion_compatibility(self):
        """Test that bit reversal enables future expansion scenarios"""
        # Test with different prefix sizes to simulate future expansion
        test_cases = [
            (1, 12),      # Small prefix, current 12-bit limit
            (123, 12),    # Medium prefix, current 12-bit limit
            (4095, 12),   # Maximum current prefix, 12-bit limit
        ]

        for prefix_value, bit_count in test_cases:
            with self.subTest(prefix=prefix_value, bits=bit_count):
                org = Organization(name=f'Test Org {prefix_value}', code=f'TEST{prefix_value}', rfid_prefix=prefix_value)
                item = Item(organization=org)
                item._item_code = bytes.fromhex('123456')

                # Test current implementation
                epc_int = item.generate_epc()
                self.assertIsNotNone(epc_int)

                # Verify bit reversal creates expansion-friendly pattern
                reversed_prefix = item._reverse_bits(prefix_value, bit_count)
                extracted_prefix = (epc_int >> 84) & 0xFFF

                self.assertEqual(extracted_prefix, reversed_prefix)

                # Verify the reversal is different from original (unless palindrome)
                if prefix_value not in [0, 4095]:  # These are palindromes in 12 bits
                    self.assertNotEqual(reversed_prefix, prefix_value)

    def test_epc_storage_on_save(self):
        """Test that EPC is automatically stored when item is saved"""
        self.organization.rfid_prefix = 123
        self.organization.save()

        item = Item.objects.create(**self.valid_item_data)

        # EPC should be automatically generated and stored
        self.assertIsNotNone(item._epc)
        self.assertEqual(len(item._epc), 12)  # 96 bits = 12 bytes

        # Stored EPC should match generated EPC
        stored_epc_hex = item._epc.hex().upper()
        generated_epc_hex = item.epc
        self.assertEqual(stored_epc_hex, generated_epc_hex)

    def test_epc_property_uses_stored_value(self):
        """Test that EPC property uses stored value when available"""
        self.organization.rfid_prefix = 456
        self.organization.save()

        item = Item.objects.create(**self.valid_item_data)

        # Get the stored EPC
        original_stored_epc = item._epc
        original_epc_hex = item.epc

        # Modify the stored EPC directly (simulate corruption or manual change)
        fake_epc = b'\x12\x34\x56\x78\x9a\xbc\xde\xf0\x11\x22\x33\x44'
        item._epc = fake_epc
        item.save(update_fields=['_epc'])

        # EPC property should return the stored value, not generate new one
        self.assertEqual(item.epc, fake_epc.hex().upper())
        self.assertNotEqual(item.epc, original_epc_hex)

    def test_epc_fallback_to_generation(self):
        """Test that EPC property falls back to generation when no stored value"""
        self.organization.rfid_prefix = 789
        self.organization.save()

        item = Item.objects.create(**self.valid_item_data)

        # Clear the stored EPC
        item._epc = None
        item.save(update_fields=['_epc'])

        # EPC property should fall back to on-the-fly generation
        epc_hex = item.epc
        self.assertIsNotNone(epc_hex)
        self.assertEqual(len(epc_hex), 24)

    def test_update_epc_method(self):
        """Test the update_epc method"""
        self.organization.rfid_prefix = 321
        self.organization.save()

        item = Item.objects.create(**self.valid_item_data)
        original_epc = item._epc

        # Update EPC without force should not change existing EPC
        result = item.update_epc(force=False)
        self.assertFalse(result)
        self.assertEqual(item._epc, original_epc)

        # Update EPC with force should regenerate
        result = item.update_epc(force=True)
        self.assertTrue(result)
        # Should be the same since nothing changed in the underlying data
        self.assertEqual(item._epc, original_epc)

        # Clear EPC and update without force should regenerate
        item._epc = None
        item.save(update_fields=['_epc'])
        result = item.update_epc(force=False)
        self.assertTrue(result)
        self.assertIsNotNone(item._epc)

    def test_search_by_epc_full_match(self):
        """Test searching by full EPC"""
        self.organization.rfid_prefix = 100
        self.organization.save()

        # Create test items
        item1 = Item.objects.create(item_name='Item 1', organization=self.organization)
        item2 = Item.objects.create(item_name='Item 2', organization=self.organization)

        # Search by full EPC
        results = Item.search_by_epc(item1.epc)
        self.assertEqual(results.count(), 1)
        self.assertEqual(results.first(), item1)

        # Search by full EPC (case insensitive)
        results = Item.search_by_epc(item1.epc.lower())
        self.assertEqual(results.count(), 1)
        self.assertEqual(results.first(), item1)

    def test_search_by_epc_partial_match(self):
        """Test searching by partial EPC"""
        self.organization.rfid_prefix = 200
        self.organization.save()

        # Create test items
        item1 = Item.objects.create(item_name='Item 1', organization=self.organization)
        item2 = Item.objects.create(item_name='Item 2', organization=self.organization)

        # Search by first 8 characters (should match items from same org)
        partial_epc = item1.epc[:8]
        results = Item.search_by_epc(partial_epc)
        self.assertGreaterEqual(results.count(), 1)
        self.assertIn(item1, results)

    def test_search_by_epc_organization_filter(self):
        """Test EPC search with organization filter"""
        # Create second organization
        org2 = Organization.objects.create(name='Org 2', code='ORG002', rfid_prefix=300)

        self.organization.rfid_prefix = 400
        self.organization.save()

        # Create items in different organizations
        item1 = Item.objects.create(item_name='Item 1', organization=self.organization)
        item2 = Item.objects.create(item_name='Item 2', organization=org2)

        # Search without organization filter
        results = Item.search_by_epc(item1.epc[:8])
        self.assertGreaterEqual(results.count(), 1)

        # Search with organization filter
        results = Item.search_by_epc(item1.epc[:8], organization=self.organization)
        self.assertIn(item1, results)
        self.assertNotIn(item2, results)

    def test_search_by_epc_invalid_input(self):
        """Test EPC search with invalid input"""
        # Empty query
        results = Item.search_by_epc('')
        self.assertEqual(results.count(), 0)

        # Invalid hex characters
        results = Item.search_by_epc('GGHHII')
        self.assertEqual(results.count(), 0)

        # None input
        results = Item.search_by_epc(None)
        self.assertEqual(results.count(), 0)

    def test_search_by_epc_hex_pattern(self):
        """Test EPC search using hex pattern matching"""
        self.organization.rfid_prefix = 500
        self.organization.save()

        # Create test items
        item1 = Item.objects.create(item_name='Item 1', organization=self.organization)
        item2 = Item.objects.create(item_name='Item 2', organization=self.organization)

        # Search using SQL LIKE pattern
        pattern = item1.epc[:4] + '%'  # First 4 chars + wildcard
        results = Item.search_by_epc_hex_pattern(pattern)
        self.assertGreaterEqual(results.count(), 1)
        self.assertIn(item1, results)

    def test_search_by_epc_no_stored_epcs(self):
        """Test EPC search when no items have stored EPCs"""
        # Create item without stored EPC
        item = Item(**self.valid_item_data)
        item._item_code = bytes.fromhex('123456')
        item._epc = None  # No stored EPC
        item.save()

        # Search should return no results since we only search stored EPCs
        results = Item.search_by_epc('123456')
        self.assertEqual(results.count(), 0)


class ManagedListValueModelTest(TestCase):
    """Test cases for the ManagedListValue model"""

    def setUp(self):
        """Set up test data"""
        self.organization = Organization.objects.create(
            name='Test Org',
            code='TEST001',
            rfid_prefix=1  # Ensure test organization has a valid rfid_prefix
        )

        self.valid_list_value_data = {
            'organization': self.organization,
            'list_name': 'ItemTypes',
            'value': 'Electronics',
            'is_active': True
        }

    def test_create_managed_list_value(self):
        """Test creating a managed list value"""
        mlv = ManagedListValue.objects.create(**self.valid_list_value_data)

        self.assertEqual(mlv.organization, self.organization)
        self.assertEqual(mlv.list_name, 'ItemTypes')
        self.assertEqual(mlv.value, 'Electronics')
        self.assertTrue(mlv.is_active)

    def test_managed_list_value_str_representation(self):
        """Test string representation"""
        mlv = ManagedListValue.objects.create(**self.valid_list_value_data)
        expected_str = "Electronics (ItemTypes)"
        self.assertEqual(str(mlv), expected_str)

    def test_list_name_choices(self):
        """Test valid list name choices"""
        valid_choices = ['ItemTypes', 'ItemStatuses']

        for choice in valid_choices:
            data = self.valid_list_value_data.copy()
            data['list_name'] = choice
            data['value'] = f'Test {choice}'

            mlv = ManagedListValue.objects.create(**data)
            self.assertEqual(mlv.list_name, choice)

    def test_unique_together_constraint(self):
        """Test unique_together constraint"""
        # Create first value
        ManagedListValue.objects.create(**self.valid_list_value_data)

        # Try to create duplicate
        with self.assertRaises(IntegrityError):
            ManagedListValue.objects.create(**self.valid_list_value_data)

        # But different organization should work
        org2 = Organization.objects.create(
            name='Test Org 2',
            code='UNIQUE003',
            rfid_prefix=10  # Ensure test organization has a valid rfid_prefix
        )

        data = self.valid_list_value_data.copy()
        data['organization'] = org2

        mlv2 = ManagedListValue.objects.create(**data)
        self.assertEqual(mlv2.organization, org2)

    def test_relationship_with_items(self):
        """Test relationship with Item model"""
        # Create list values
        item_type = ManagedListValue.objects.create(**self.valid_list_value_data)

        status_data = self.valid_list_value_data.copy()
        status_data['list_name'] = 'ItemStatuses'
        status_data['value'] = 'Available'
        item_status = ManagedListValue.objects.create(**status_data)

        # Create item using these values
        item = Item.objects.create(
            item_name='Test Item',
            organization=self.organization,
            item_type=item_type,
            status=item_status
        )

        # Test reverse relationships
        self.assertIn(item, item_type.items_of_type.all())
        self.assertIn(item, item_status.items_with_status.all())

    def test_default_values(self):
        """Test default values"""
        minimal_data = {
            'organization': self.organization,
            'list_name': 'ItemTypes',
            'value': 'Test Type'
        }

        mlv = ManagedListValue.objects.create(**minimal_data)
        self.assertTrue(mlv.is_active)  # Should default to True


class OrganizationUserModelTest(TestCase):
    """Test cases for the OrganizationUser model"""

    def setUp(self):
        """Set up test data"""
        self.organization = Organization.objects.create(
            name='Test Org',
            code='TEST001',
            rfid_prefix=2  # Ensure test organization has a valid rfid_prefix
        )

        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        self.valid_org_user_data = {
            'user': self.user,
            'organization': self.organization,
            'is_admin': False,
            'can_edit': True,
            'can_add': True
        }

    def test_create_organization_user(self):
        """Test creating an organization user"""
        org_user = OrganizationUser.objects.create(**self.valid_org_user_data)

        self.assertEqual(org_user.user, self.user)
        self.assertEqual(org_user.organization, self.organization)
        self.assertFalse(org_user.is_admin)
        self.assertTrue(org_user.can_edit)
        self.assertTrue(org_user.can_add)

    def test_organization_user_str_representation(self):
        """Test string representation"""
        org_user = OrganizationUser.objects.create(**self.valid_org_user_data)
        expected_str = f"{self.user.username} in {self.organization.name}"
        self.assertEqual(str(org_user), expected_str)

    def test_unique_together_constraint(self):
        """Test unique_together constraint for user and organization"""
        # Create first relationship
        OrganizationUser.objects.create(**self.valid_org_user_data)

        # Try to create duplicate
        with self.assertRaises(IntegrityError):
            OrganizationUser.objects.create(**self.valid_org_user_data)

        # But same user in different organization should work
        org2 = Organization.objects.create(
            name='Test Org 2',
            code='ORGUSER003',
            rfid_prefix=11  # Ensure test organization has a valid rfid_prefix
        )

        data = self.valid_org_user_data.copy()
        data['organization'] = org2

        org_user2 = OrganizationUser.objects.create(**data)
        self.assertEqual(org_user2.organization, org2)

    def test_default_permission_values(self):
        """Test default values for permissions"""
        minimal_data = {
            'user': self.user,
            'organization': self.organization
        }

        org_user = OrganizationUser.objects.create(**minimal_data)

        self.assertFalse(org_user.is_admin)  # Default False
        self.assertTrue(org_user.can_edit)   # Default True
        self.assertTrue(org_user.can_add)    # Default True

    def test_admin_user_creation(self):
        """Test creating an admin user"""
        admin_data = self.valid_org_user_data.copy()
        admin_data['is_admin'] = True

        org_user = OrganizationUser.objects.create(**admin_data)
        self.assertTrue(org_user.is_admin)

    def test_restricted_user_creation(self):
        """Test creating a user with restricted permissions"""
        restricted_data = self.valid_org_user_data.copy()
        restricted_data.update({
            'can_edit': False,
            'can_add': False
        })

        org_user = OrganizationUser.objects.create(**restricted_data)
        self.assertFalse(org_user.can_edit)
        self.assertFalse(org_user.can_add)

    def test_many_to_many_relationship(self):
        """Test the many-to-many relationship through OrganizationUser"""
        # Create organization user relationship
        OrganizationUser.objects.create(**self.valid_org_user_data)

        # Test forward relationship
        user_orgs = self.user.organizations.all()
        self.assertIn(self.organization, user_orgs)

        # Test reverse relationship
        org_users = self.organization.users.all()
        self.assertIn(self.user, org_users)


class ItemImageModelTest(TestCase):
    """Test cases for the ItemImage model"""

    def setUp(self):
        """Set up test data"""
        self.organization = Organization.objects.create(
            name='Test Org',
            code='TEST001',
            rfid_prefix=3  # Ensure test organization has a valid rfid_prefix
        )

        self.item = Item.objects.create(
            item_name='Test Item',
            organization=self.organization
        )

        # Create test image content
        self.image_content = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'

        self.valid_image_data = {
            'item': self.item,
            'image': SimpleUploadedFile("test.png", self.image_content, content_type="image/png"),
            'caption': 'Test image caption',
            'is_primary': False,
            'order': 0
        }

    def test_create_item_image(self):
        """Test creating an item image"""
        item_image = ItemImage.objects.create(**self.valid_image_data)

        self.assertEqual(item_image.item, self.item)
        self.assertTrue(item_image.image)
        self.assertEqual(item_image.caption, 'Test image caption')
        self.assertFalse(item_image.is_primary)
        self.assertEqual(item_image.order, 0)

    def test_item_image_str_representation(self):
        """Test string representation"""
        item_image = ItemImage.objects.create(**self.valid_image_data)
        expected_str = f"Image for {self.item.item_name}"
        self.assertEqual(str(item_image), expected_str)

    def test_primary_image_enforcement(self):
        """Test that only one image can be primary per item"""
        # Create first image as primary
        image1_data = self.valid_image_data.copy()
        image1_data['image'] = SimpleUploadedFile("test1.png", self.image_content, content_type="image/png")
        image1_data['is_primary'] = True
        image1 = ItemImage.objects.create(**image1_data)

        self.assertTrue(image1.is_primary)

        # Create second image as primary
        image2_data = self.valid_image_data.copy()
        image2_data['image'] = SimpleUploadedFile("test2.png", self.image_content, content_type="image/png")
        image2_data['is_primary'] = True
        image2_data['order'] = 1
        image2 = ItemImage.objects.create(**image2_data)

        # Refresh first image from database
        image1.refresh_from_db()

        # Only the second image should be primary now
        self.assertFalse(image1.is_primary)
        self.assertTrue(image2.is_primary)

    def test_image_ordering(self):
        """Test image ordering"""
        images = []
        for i in range(3):
            image_data = self.valid_image_data.copy()
            image_data['image'] = SimpleUploadedFile(f"test{i}.png", self.image_content, content_type="image/png")
            image_data['order'] = i
            image_data['caption'] = f'Image {i}'
            images.append(ItemImage.objects.create(**image_data))

        # Get images in order
        ordered_images = list(ItemImage.objects.filter(item=self.item).order_by('order'))

        for i, image in enumerate(ordered_images):
            self.assertEqual(image.order, i)
            self.assertEqual(image.caption, f'Image {i}')

    def test_relationship_with_item(self):
        """Test relationship with Item model"""
        item_image = ItemImage.objects.create(**self.valid_image_data)

        # Test forward relationship
        self.assertEqual(item_image.item, self.item)

        # Test reverse relationship
        item_images = self.item.item_images.all()
        self.assertIn(item_image, item_images)

    def test_default_values(self):
        """Test default values"""
        minimal_data = {
            'item': self.item,
            'image': SimpleUploadedFile("minimal.png", self.image_content, content_type="image/png")
        }

        item_image = ItemImage.objects.create(**minimal_data)

        self.assertEqual(item_image.caption, '')  # Default empty string
        self.assertFalse(item_image.is_primary)   # Default False
        self.assertEqual(item_image.order, 0)     # Default 0

    def test_image_upload_path(self):
        """Test that images are uploaded to correct path"""
        item_image = ItemImage.objects.create(**self.valid_image_data)

        # Check that image path starts with 'item_images/'
        self.assertTrue(item_image.image.name.startswith('item_images/'))


class IntegrationTest(TestCase):
    """Integration tests for model interactions"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        self.organization = Organization.objects.create(
            name='Test Organization',
            code='TEST001',
            rfid_prefix=4  # Ensure test organization has a valid rfid_prefix
        )

        # Create organization user relationship
        self.org_user = OrganizationUser.objects.create(
            user=self.user,
            organization=self.organization,
            is_admin=True
        )

    def test_complete_item_workflow(self):
        """Test complete item creation workflow with all relationships"""
        # Create managed list values
        item_type = ManagedListValue.objects.create(
            organization=self.organization,
            list_name='ItemTypes',
            value='Electronics'
        )

        item_status = ManagedListValue.objects.create(
            organization=self.organization,
            list_name='ItemStatuses',
            value='Available'
        )

        # Create container item
        container = Item.objects.create(
            item_name='Storage Box',
            item_description='A container for storing items',
            organization=self.organization,
            item_type=item_type,
            status=item_status
        )

        # Create item inside container
        item = Item.objects.create(
            item_name='Laptop',
            item_description='Dell Laptop',
            organization=self.organization,
            item_type=item_type,
            status=item_status,
            located_in=container
        )

        # Add image to item
        image_content = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
        item_image = ItemImage.objects.create(
            item=item,
            image=SimpleUploadedFile("laptop.png", image_content, content_type="image/png"),
            caption='Laptop photo',
            is_primary=True
        )

        # Verify all relationships work
        self.assertEqual(item.organization, self.organization)
        self.assertEqual(item.located_in, container)
        self.assertEqual(container.contained_item_count, 1)
        self.assertEqual(item.item_type, item_type)
        self.assertEqual(item.status, item_status)
        self.assertIn(item, item_type.items_of_type.all())
        self.assertIn(item, item_status.items_with_status.all())
        self.assertIn(item_image, item.item_images.all())



        # Verify organization user relationship
        self.assertIn(self.organization, self.user.organizations.all())
        self.assertTrue(self.org_user.is_admin)


class LocationBreadcrumbFilterTest(TestCase):
    """Test cases for the location_breadcrumb template filter"""

    def setUp(self):
        """Set up test data"""
        self.organization = Organization.objects.create(
            name='Test Org',
            code='TEST001',
            rfid_prefix=5  # Ensure test organization has a valid rfid_prefix
        )

    def test_location_breadcrumb_no_location(self):
        """Test breadcrumb for item with no location"""
        item = Item.objects.create(
            item_name='Top Level Item',
            organization=self.organization
        )

        result = location_breadcrumb(item)
        self.assertIn('Top Level', result)
        self.assertIn('text-muted', result)

    def test_location_breadcrumb_single_level(self):
        """Test breadcrumb for item with one level of location"""
        container = Item.objects.create(
            item_name='Container',
            organization=self.organization
        )

        item = Item.objects.create(
            item_name='Item',
            organization=self.organization,
            located_in=container
        )

        result = location_breadcrumb(item)
        expected_url = reverse('inventory:item_by_code', args=[container.item_code])

        self.assertIn(container.item_name, result)
        self.assertIn(expected_url, result)
        self.assertIn('<a href=', result)

    def test_location_breadcrumb_two_levels(self):
        """Test breadcrumb for item with two levels of location hierarchy"""
        grandparent = Item.objects.create(
            item_name='Grandparent Container',
            organization=self.organization
        )

        parent = Item.objects.create(
            item_name='Parent Container',
            organization=self.organization,
            located_in=grandparent
        )

        item = Item.objects.create(
            item_name='Item',
            organization=self.organization,
            located_in=parent
        )

        result = location_breadcrumb(item)
        parent_url = reverse('inventory:item_by_code', args=[parent.item_code])
        grandparent_url = reverse('inventory:item_by_code', args=[grandparent.item_code])

        # Should show parent > grandparent format
        self.assertIn(parent.item_name, result)
        self.assertIn(grandparent.item_name, result)
        self.assertIn(parent_url, result)
        self.assertIn(grandparent_url, result)
        self.assertIn('&gt;', result)  # HTML encoded >

    def test_location_breadcrumb_none_item(self):
        """Test breadcrumb with None item"""
        result = location_breadcrumb(None)
        self.assertIn('Top Level', result)
        self.assertIn('text-muted', result)


class ItemCommentModelTest(TestCase):
    """Test cases for the ItemComment model"""

    def setUp(self):
        """Set up test data"""
        self.organization = Organization.objects.create(
            name='Test Org',
            code='TEST001',
            rfid_prefix=6
        )

        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        self.item = Item.objects.create(
            item_name='Test Item',
            organization=self.organization
        )

        self.comment_reason = ManagedListValue.objects.create(
            organization=self.organization,
            list_name='CommentReasons',
            value='Needs Repair',
            is_active=True
        )

    def test_create_authenticated_user_comment(self):
        """Test creating a comment by an authenticated user"""
        comment = ItemComment.objects.create(
            item=self.item,
            comment_text='This item needs attention',
            user=self.user,
            reason=self.comment_reason
        )

        self.assertEqual(comment.item, self.item)
        self.assertEqual(comment.comment_text, 'This item needs attention')
        self.assertEqual(comment.user, self.user)
        self.assertEqual(comment.reason, self.comment_reason)
        self.assertEqual(comment.guest_name, '')
        self.assertFalse(comment.is_guest_comment)
        self.assertFalse(comment.is_edited)

    def test_create_guest_comment(self):
        """Test creating a comment by a guest user"""
        comment = ItemComment.objects.create(
            item=self.item,
            comment_text='Guest comment here',
            guest_name='John Doe'
        )

        self.assertEqual(comment.item, self.item)
        self.assertEqual(comment.comment_text, 'Guest comment here')
        self.assertIsNone(comment.user)
        self.assertEqual(comment.guest_name, 'John Doe')
        self.assertTrue(comment.is_guest_comment)

    def test_comment_str_representation(self):
        """Test string representation of comments"""
        # Authenticated user comment
        auth_comment = ItemComment.objects.create(
            item=self.item,
            comment_text='Auth comment',
            user=self.user
        )
        expected_str = f"Comment by {self.user.username} on {self.item.item_name}"
        self.assertEqual(str(auth_comment), expected_str)

        # Guest comment
        guest_comment = ItemComment.objects.create(
            item=self.item,
            comment_text='Guest comment',
            guest_name='Jane Smith'
        )
        expected_str = f"Comment by Guest: Jane Smith on {self.item.item_name}"
        self.assertEqual(str(guest_comment), expected_str)

    def test_commenter_name_property(self):
        """Test commenter_name property"""
        # Test with authenticated user (with full name)
        self.user.first_name = 'John'
        self.user.last_name = 'Doe'
        self.user.save()

        auth_comment = ItemComment.objects.create(
            item=self.item,
            comment_text='Auth comment',
            user=self.user
        )
        self.assertEqual(auth_comment.commenter_name, 'John Doe')

        # Test with authenticated user (username only)
        user2 = User.objects.create_user(username='testuser2')
        auth_comment2 = ItemComment.objects.create(
            item=self.item,
            comment_text='Auth comment 2',
            user=user2
        )
        self.assertEqual(auth_comment2.commenter_name, 'testuser2')

        # Test with guest user
        guest_comment = ItemComment.objects.create(
            item=self.item,
            comment_text='Guest comment',
            guest_name='Jane Smith'
        )
        self.assertEqual(guest_comment.commenter_name, 'Jane Smith')

    def test_comment_validation_both_user_and_guest(self):
        """Test validation fails when both user and guest_name are provided"""
        with self.assertRaises(ValidationError):
            comment = ItemComment(
                item=self.item,
                comment_text='Invalid comment',
                user=self.user,
                guest_name='John Doe'
            )
            comment.clean()

    def test_comment_validation_neither_user_nor_guest(self):
        """Test validation fails when neither user nor guest_name are provided"""
        with self.assertRaises(ValidationError):
            comment = ItemComment(
                item=self.item,
                comment_text='Invalid comment'
            )
            comment.clean()

    def test_comment_ordering(self):
        """Test that comments are ordered by creation date (newest first)"""
        comment1 = ItemComment.objects.create(
            item=self.item,
            comment_text='First comment',
            user=self.user
        )

        comment2 = ItemComment.objects.create(
            item=self.item,
            comment_text='Second comment',
            guest_name='Guest User'
        )

        comments = list(ItemComment.objects.filter(item=self.item))
        self.assertEqual(comments[0], comment2)  # Newest first
        self.assertEqual(comments[1], comment1)

    def test_comment_with_photo(self):
        """Test comment with photo attachment"""
        # Create a simple test image
        image_content = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'

        comment = ItemComment.objects.create(
            item=self.item,
            comment_text='Comment with photo',
            user=self.user,
            photo=SimpleUploadedFile("test.jpg", image_content, content_type="image/jpeg")
        )

        self.assertTrue(comment.photo)
        self.assertTrue(comment.photo.name.startswith('comment_photos/'))

    def test_comment_edit_tracking(self):
        """Test that editing a comment sets is_edited flag"""
        comment = ItemComment.objects.create(
            item=self.item,
            comment_text='Original comment',
            user=self.user
        )

        self.assertFalse(comment.is_edited)

        # Simulate editing
        comment.comment_text = 'Edited comment'
        comment.is_edited = True
        comment.save()

        self.assertTrue(comment.is_edited)

    def test_comment_relationship_with_item(self):
        """Test relationship with Item model"""
        comment = ItemComment.objects.create(
            item=self.item,
            comment_text='Test comment',
            user=self.user
        )

        # Test forward relationship
        self.assertEqual(comment.item, self.item)

        # Test reverse relationship
        item_comments = self.item.comments.all()
        self.assertIn(comment, item_comments)

    def test_comment_relationship_with_reason(self):
        """Test relationship with ManagedListValue (reason)"""
        comment = ItemComment.objects.create(
            item=self.item,
            comment_text='Test comment with reason',
            user=self.user,
            reason=self.comment_reason
        )

        # Test forward relationship
        self.assertEqual(comment.reason, self.comment_reason)

        # Test reverse relationship
        reason_comments = self.comment_reason.comments_with_reason.all()
        self.assertIn(comment, reason_comments)


class ItemCommentArchiveTest(TestCase):
    """Test cases for the ItemComment archive functionality"""

    def setUp(self):
        """Set up test data"""
        self.organization = Organization.objects.create(
            name='Test Org',
            code='TEST001',
            rfid_prefix=6
        )

        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        self.other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='testpass123'
        )

        self.item = Item.objects.create(
            item_name='Test Item',
            organization=self.organization
        )

        self.comment = ItemComment.objects.create(
            item=self.item,
            comment_text='Test comment',
            user=self.user
        )

        self.guest_comment = ItemComment.objects.create(
            item=self.item,
            comment_text='Guest comment',
            guest_name='John Doe'
        )

    def test_comment_archive_method(self):
        """Test the archive method on ItemComment"""
        # Initially not archived
        self.assertFalse(self.comment.is_archived)
        self.assertIsNone(self.comment.archived_at)
        self.assertIsNone(self.comment.archived_by)

        # Archive the comment
        result = self.comment.archive(self.user)
        self.assertTrue(result)

        # Refresh from database
        self.comment.refresh_from_db()

        # Check archive status
        self.assertTrue(self.comment.is_archived)
        self.assertIsNotNone(self.comment.archived_at)
        self.assertEqual(self.comment.archived_by, self.user)

        # Try to archive again (should return False)
        result = self.comment.archive(self.user)
        self.assertFalse(result)

    def test_comment_unarchive_method(self):
        """Test the unarchive method on ItemComment"""
        # Archive first
        self.comment.archive(self.user)
        self.assertTrue(self.comment.is_archived)

        # Unarchive the comment
        result = self.comment.unarchive()
        self.assertTrue(result)

        # Refresh from database
        self.comment.refresh_from_db()

        # Check unarchive status
        self.assertFalse(self.comment.is_archived)
        self.assertIsNone(self.comment.archived_at)
        self.assertIsNone(self.comment.archived_by)

        # Try to unarchive again (should return False)
        result = self.comment.unarchive()
        self.assertFalse(result)

    def test_can_archive_permissions(self):
        """Test can_archive permission method"""
        # Authenticated user can archive
        self.assertTrue(self.comment.can_archive(self.user))
        self.assertTrue(self.comment.can_archive(self.other_user))

        # Anonymous user cannot archive
        from django.contrib.auth.models import AnonymousUser
        anonymous_user = AnonymousUser()
        self.assertFalse(self.comment.can_archive(anonymous_user))
        self.assertFalse(self.comment.can_archive(None))

    def test_archive_status_display_property(self):
        """Test archive_status_display property"""
        # Active comment
        self.assertEqual(self.comment.archive_status_display, "Active")

        # Archive the comment
        self.comment.archive(self.user)
        status_display = self.comment.archive_status_display
        self.assertIn("Archived on", status_display)
        self.assertIn(self.user.username, status_display)

    def test_comment_queryset_filtering(self):
        """Test that archived comments are properly filtered"""
        # Create another comment and archive it
        archived_comment = ItemComment.objects.create(
            item=self.item,
            comment_text='Archived comment',
            user=self.user
        )
        archived_comment.archive(self.user)

        # Test filtering active comments
        active_comments = ItemComment.objects.filter(is_archived=False)
        self.assertIn(self.comment, active_comments)
        self.assertIn(self.guest_comment, active_comments)
        self.assertNotIn(archived_comment, active_comments)

        # Test filtering archived comments
        archived_comments = ItemComment.objects.filter(is_archived=True)
        self.assertNotIn(self.comment, archived_comments)
        self.assertNotIn(self.guest_comment, archived_comments)
        self.assertIn(archived_comment, archived_comments)

    def test_comment_ordering_with_archive(self):
        """Test that comment ordering works correctly with archived comments"""
        # Create comments in sequence
        comment1 = ItemComment.objects.create(
            item=self.item,
            comment_text='First comment',
            user=self.user
        )

        comment2 = ItemComment.objects.create(
            item=self.item,
            comment_text='Second comment',
            user=self.user
        )

        comment3 = ItemComment.objects.create(
            item=self.item,
            comment_text='Third comment',
            user=self.user
        )

        # Archive the middle comment
        comment2.archive(self.user)

        # Get active comments (should be newest first, excluding archived)
        active_comments = list(ItemComment.objects.filter(
            item=self.item,
            is_archived=False
        ).order_by('-created_at'))

        # Should have comment3, comment1, self.comment, self.guest_comment (newest first)
        self.assertEqual(active_comments[0], comment3)
        self.assertEqual(active_comments[1], comment1)
        self.assertNotIn(comment2, active_comments)

    def test_guest_comment_archive(self):
        """Test archiving guest comments"""
        # Guest comment should be archivable by any authenticated user
        self.assertTrue(self.guest_comment.can_archive(self.user))

        # Archive guest comment
        result = self.guest_comment.archive(self.user)
        self.assertTrue(result)

        # Check archive status
        self.guest_comment.refresh_from_db()
        self.assertTrue(self.guest_comment.is_archived)
        self.assertEqual(self.guest_comment.archived_by, self.user)


class ItemCommentArchiveViewTest(TestCase):
    """Test cases for archive-related views"""

    def setUp(self):
        """Set up test data"""
        self.organization = Organization.objects.create(
            name='Test Org',
            code='TEST001',
            rfid_prefix=6
        )

        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        self.item = Item.objects.create(
            item_name='Test Item',
            organization=self.organization
        )

        self.comment = ItemComment.objects.create(
            item=self.item,
            comment_text='Test comment',
            user=self.user
        )

    def test_archive_comment_view_authenticated(self):
        """Test archive comment view for authenticated users"""
        self.client.login(username='testuser', password='testpass123')

        # Test GET request (confirmation page)
        response = self.client.get(f'/inventory/comment/{self.comment.id}/archive/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Archive Comment')
        self.assertContains(response, self.comment.comment_text)

        # Test POST request (actual archive)
        response = self.client.post(f'/inventory/comment/{self.comment.id}/archive/')
        self.assertEqual(response.status_code, 302)  # Redirect after success

        # Check comment was archived
        self.comment.refresh_from_db()
        self.assertTrue(self.comment.is_archived)
        self.assertEqual(self.comment.archived_by, self.user)

    def test_archive_comment_view_unauthenticated(self):
        """Test archive comment view for unauthenticated users"""
        response = self.client.get(f'/inventory/comment/{self.comment.id}/archive/')
        self.assertEqual(response.status_code, 302)  # Redirect to login

        response = self.client.post(f'/inventory/comment/{self.comment.id}/archive/')
        self.assertEqual(response.status_code, 302)  # Redirect to login

        # Comment should not be archived
        self.comment.refresh_from_db()
        self.assertFalse(self.comment.is_archived)

    def test_unarchive_comment_view_authenticated(self):
        """Test unarchive comment view for authenticated users"""
        # Archive the comment first
        self.comment.archive(self.user)

        self.client.login(username='testuser', password='testpass123')

        # Test GET request (confirmation page)
        response = self.client.get(f'/inventory/comment/{self.comment.id}/unarchive/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Unarchive Comment')
        self.assertContains(response, self.comment.comment_text)

        # Test POST request (actual unarchive)
        response = self.client.post(f'/inventory/comment/{self.comment.id}/unarchive/')
        self.assertEqual(response.status_code, 302)  # Redirect after success

        # Check comment was unarchived
        self.comment.refresh_from_db()
        self.assertFalse(self.comment.is_archived)
        self.assertIsNone(self.comment.archived_by)

    def test_item_detail_view_with_archived_comments(self):
        """Test item detail view filtering of archived comments"""
        # Create additional comments
        active_comment = ItemComment.objects.create(
            item=self.item,
            comment_text='Active comment',
            user=self.user
        )

        archived_comment = ItemComment.objects.create(
            item=self.item,
            comment_text='Archived comment',
            user=self.user
        )
        archived_comment.archive(self.user)

        self.client.login(username='testuser', password='testpass123')

        # Test default view (should not show archived comments)
        response = self.client.get(f'/inventory/item/{self.item.item_code}/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Active comment')
        self.assertContains(response, 'Test comment')
        self.assertNotContains(response, 'Archived comment')

        # Test with show_archived=true
        response = self.client.get(f'/inventory/item/{self.item.item_code}/?show_archived=true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Active comment')
        self.assertContains(response, 'Test comment')
        self.assertContains(response, 'Archived comment')

    def test_recent_comments_view_with_archived_comments(self):
        """Test recent comments view filtering of archived comments"""
        # Create additional comments
        active_comment = ItemComment.objects.create(
            item=self.item,
            comment_text='Active comment',
            user=self.user
        )

        archived_comment = ItemComment.objects.create(
            item=self.item,
            comment_text='Archived comment',
            user=self.user
        )
        archived_comment.archive(self.user)

        self.client.login(username='testuser', password='testpass123')

        # Set organization in session
        session = self.client.session
        session['org_id'] = self.organization.id
        session.save()

        # Test default view (should not show archived comments)
        response = self.client.get('/inventory/comments/recent/')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Active comment')
        self.assertContains(response, 'Test comment')
        self.assertNotContains(response, 'Archived comment')

        # Test with show_archived=true
        response = self.client.get('/inventory/comments/recent/?show_archived=true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Active comment')
        self.assertContains(response, 'Test comment')
        self.assertContains(response, 'Archived comment')

    def test_archive_nonexistent_comment(self):
        """Test archiving a non-existent comment"""
        self.client.login(username='testuser', password='testpass123')

        response = self.client.post('/inventory/comment/99999/archive/')
        self.assertEqual(response.status_code, 404)

    def test_archive_already_archived_comment(self):
        """Test archiving an already archived comment"""
        self.comment.archive(self.user)

        self.client.login(username='testuser', password='testpass123')

        response = self.client.post(f'/inventory/comment/{self.comment.id}/archive/')
        self.assertEqual(response.status_code, 302)  # Should still redirect

        # Comment should remain archived
        self.comment.refresh_from_db()
        self.assertTrue(self.comment.is_archived)


class OrganizationSwitchingTest(TestCase):
    """Test cases for organization switching functionality"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()

        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create test organizations
        self.org1 = Organization.objects.create(
            name='Organization 1',
            code='ORG001',
            rfid_prefix=1
        )

        self.org2 = Organization.objects.create(
            name='Organization 2',
            code='ORG002',
            rfid_prefix=2
        )

        # Create organization user relationships
        OrganizationUser.objects.create(
            user=self.user,
            organization=self.org1,
            is_admin=True
        )

        OrganizationUser.objects.create(
            user=self.user,
            organization=self.org2,
            can_edit=True,
            can_add=True
        )

    def test_organization_switching_success(self):
        """Test successful organization switching"""
        self.client.login(username='testuser', password='testpass123')

        # Switch to organization 1
        response = self.client.post(
            reverse('inventory:switch_organization', args=[self.org1.id]),
            {'next': '/inventory/items/'}
        )

        # Should redirect properly
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, '/inventory/items/')

        # Check session has org_id
        session = self.client.session
        self.assertEqual(session.get('org_id'), str(self.org1.id))

        # Check success message
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('Switched to Organization 1' in str(m) for m in messages))

    def test_organization_switching_with_backup_cookie(self):
        """Test that backup cookie is set during organization switching"""
        self.client.login(username='testuser', password='testpass123')

        response = self.client.post(
            reverse('inventory:switch_organization', args=[self.org2.id]),
            {'next': '/inventory/items/'}
        )

        # Check backup cookie is set
        self.assertIn('backup_org_id', response.cookies)
        self.assertEqual(response.cookies['backup_org_id'].value, str(self.org2.id))

    def test_organization_switching_invalid_org(self):
        """Test organization switching with invalid organization ID"""
        self.client.login(username='testuser', password='testpass123')

        response = self.client.post(
            reverse('inventory:switch_organization', args=[999]),  # Non-existent org
            {'next': '/inventory/items/'}
        )

        # Should return 404
        self.assertEqual(response.status_code, 404)

    def test_organization_switching_unauthorized_org(self):
        """Test organization switching to unauthorized organization"""
        # Create organization user doesn't have access to
        org3 = Organization.objects.create(
            name='Organization 3',
            code='ORG003',
            rfid_prefix=3
        )

        self.client.login(username='testuser', password='testpass123')

        response = self.client.post(
            reverse('inventory:switch_organization', args=[org3.id]),
            {'next': '/inventory/items/'}
        )

        # Should return 404 (organization exists but user has no access)
        self.assertEqual(response.status_code, 404)

    def test_organization_selection_page_loads(self):
        """Test that organization selection page loads correctly"""
        self.client.login(username='testuser', password='testpass123')

        response = self.client.get(reverse('inventory:select_organization'))

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Organization 1')
        self.assertContains(response, 'Organization 2')
        self.assertContains(response, 'Select Organization')

    def test_logout_from_organization_selection(self):
        """Test logout functionality from organization selection page"""
        self.client.login(username='testuser', password='testpass123')

        response = self.client.post(reverse('inventory:logout'))

        # Should redirect to login page
        self.assertEqual(response.status_code, 302)
        self.assertIn('login', response.url)

        # Check success message
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('successfully logged out' in str(m) for m in messages))

    def test_session_persistence_after_switching(self):
        """Test that session persists after organization switching"""
        self.client.login(username='testuser', password='testpass123')

        # Switch organization
        self.client.post(
            reverse('inventory:switch_organization', args=[self.org1.id]),
            {'next': '/inventory/items/'}
        )

        # Make another request to verify session persists
        response = self.client.get('/inventory/debug-session/')
        self.assertEqual(response.status_code, 200)

        # Parse JSON response
        import json
        data = json.loads(response.content)
        self.assertEqual(data['org_id'], str(self.org1.id))
        self.assertTrue(data['is_authenticated'])
