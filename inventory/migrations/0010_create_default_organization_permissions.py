# Generated manually

from django.db import migrations


def create_default_permissions(apps, schema_editor):
    """
    Assign all existing users as members of all organizations with full permissions
    """
    User = apps.get_model('auth', 'User')
    Organization = apps.get_model('inventory', 'Organization')
    OrganizationUser = apps.get_model('inventory', 'OrganizationUser')
    
    # Get all existing users and organizations
    users = User.objects.all()
    organizations = Organization.objects.all()
    
    # For each user, create membership in each organization
    for user in users:
        # Make the first user an admin in all organizations
        is_first_user = user.id == 1
        
        for org in organizations:
            OrganizationUser.objects.create(
                user=user,
                organization=org,
                is_admin=is_first_user,  # First user gets admin rights
                can_edit=True,
                can_add=True
            )


def reverse_default_permissions(apps, schema_editor):
    """
    Remove all organization user permissions
    """
    OrganizationUser = apps.get_model('inventory', 'OrganizationUser')
    OrganizationUser.objects.all().delete()


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0009_create_organizationuser'),
    ]

    operations = [
        migrations.RunPython(
            create_default_permissions,
            reverse_default_permissions
        ),
    ]