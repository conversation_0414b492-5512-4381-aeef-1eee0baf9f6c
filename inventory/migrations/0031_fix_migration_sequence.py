# Generated by Django 5.0 on 2025-06-19
# Fix migration sequence issues with rfid_prefix field

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0030_fix_rfid_prefix_nulls'),
    ]

    operations = [
        # This migration is a no-op to ensure the migration sequence is consistent
        # The actual field changes were handled in previous migrations
    ]
