# Generated manually

from django.db import migrations


def create_default_organization(apps, schema_editor):
    Organization = apps.get_model('inventory', 'Organization')
    if not Organization.objects.exists():
        Organization.objects.create(
            name='Default Organization',
            code='DEFAULT',
            is_active=True
        )


def reverse_default_organization(apps, schema_editor):
    Organization = apps.get_model('inventory', 'Organization')
    Organization.objects.filter(code='DEFAULT').delete()


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0003_create_organization'),
    ]

    operations = [
        migrations.RunPython(
            create_default_organization,
            reverse_default_organization
        ),
    ]