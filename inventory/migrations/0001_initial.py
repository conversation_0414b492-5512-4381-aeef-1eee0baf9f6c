# Generated by Django 5.0 on 2025-05-08 03:45

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ManagedListValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('list_name', models.CharField(choices=[('ItemTypes', 'Item Types'), ('ItemStatuses', 'Item Statuses')], max_length=50)),
                ('value', models.CharField(max_length=100)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'indexes': [models.Index(fields=['list_name'], name='inventory_m_list_na_668b4a_idx'), models.Index(fields=['is_active'], name='inventory_m_is_acti_b677eb_idx')],
                'unique_together': {('list_name', 'value')},
            },
        ),
        migrations.CreateModel(
            name='Item',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item_code', models.BinaryField(max_length=3, unique=True)),
                ('item_name', models.CharField(max_length=255)),
                ('item_description', models.TextField(blank=True, null=True)),
                ('images', models.JSONField(default=list)),
                ('custom_fields', models.JSONField(default=dict)),
                ('date_added', models.DateTimeField(auto_now_add=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('is_archived', models.BooleanField(default=False)),
                ('located_in', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='contained_items', to='inventory.item')),
                ('item_type', models.ForeignKey(limit_choices_to={'is_active': True, 'list_name': 'ItemTypes'}, on_delete=django.db.models.deletion.PROTECT, related_name='items_of_type', to='inventory.managedlistvalue')),
                ('status', models.ForeignKey(limit_choices_to={'is_active': True, 'list_name': 'ItemStatuses'}, on_delete=django.db.models.deletion.PROTECT, related_name='items_with_status', to='inventory.managedlistvalue')),
            ],
            options={
                'indexes': [models.Index(fields=['item_code'], name='inventory_i_item_co_e61695_idx'), models.Index(fields=['located_in'], name='inventory_i_located_901f78_idx'), models.Index(fields=['is_archived'], name='inventory_i_is_arch_8e4e5e_idx'), models.Index(fields=['item_type'], name='inventory_i_item_ty_c7c519_idx'), models.Index(fields=['status'], name='inventory_i_status__f67e12_idx'), models.Index(fields=['date_added'], name='inventory_i_date_ad_1c1413_idx'), models.Index(fields=['last_updated'], name='inventory_i_last_up_66d543_idx')],
            },
        ),
    ]
