# Generated by Django 5.0 on 2025-06-19 18:11

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0032_rename_inventory_item_epc_idx_inventory_i_epc_3e829e_idx'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='managedlistvalue',
            name='list_name',
            field=models.CharField(choices=[('ItemTypes', 'Item Types'), ('ItemStatuses', 'Item Statuses'), ('CommentReasons', 'Comment Reasons')], max_length=50),
        ),
        migrations.CreateModel(
            name='ItemComment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('comment_text', models.TextField(help_text='Comment content')),
                ('guest_name', models.CharField(blank=True, help_text='Name provided by guest user', max_length=100)),
                ('photo', models.ImageField(blank=True, help_text='Optional photo attachment (JPG only, max 10MB)', null=True, upload_to='comment_photos/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_edited', models.BooleanField(default=False)),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='inventory.item')),
                ('reason', models.ForeignKey(blank=True, help_text='Optional reason category for the comment', limit_choices_to={'is_active': True, 'list_name': 'CommentReasons'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='comments_with_reason', to='inventory.managedlistvalue')),
                ('user', models.ForeignKey(blank=True, help_text='Authenticated user who posted the comment', null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['item', '-created_at'], name='inventory_i_item_id_253f36_idx'), models.Index(fields=['-created_at'], name='inventory_i_created_194c76_idx'), models.Index(fields=['user'], name='inventory_i_user_id_e78f19_idx'), models.Index(fields=['reason'], name='inventory_i_reason__93518b_idx')],
            },
        ),
        migrations.AddConstraint(
            model_name='itemcomment',
            constraint=models.CheckConstraint(check=models.Q(models.Q(('guest_name', ''), ('user__isnull', False)), models.Q(('guest_name__isnull', False), ('user__isnull', True), models.Q(('guest_name', ''), _negated=True)), _connector='OR'), name='comment_must_have_user_or_guest_name'),
        ),
    ]
