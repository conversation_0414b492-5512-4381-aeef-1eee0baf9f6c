# Generated by Django 5.0 on 2025-06-19

from django.db import migrations, models


def ensure_all_orgs_have_rfid_prefix(apps, schema_editor):
    """Ensure all organizations have a valid rfid_prefix"""
    Organization = apps.get_model('inventory', 'Organization')
    
    # Find organizations without rfid_prefix
    orgs_without_prefix = Organization.objects.filter(rfid_prefix__isnull=True)
    
    if orgs_without_prefix.exists():
        print(f"Found {orgs_without_prefix.count()} organizations without rfid_prefix")
        
        # Get the highest existing prefix
        from django.db.models import Max
        highest = Organization.objects.aggregate(Max('rfid_prefix'))['rfid_prefix__max']
        
        # Start assigning from the next available number
        next_prefix = 0 if highest is None else highest + 1
        
        for org in orgs_without_prefix:
            org.rfid_prefix = next_prefix
            org.save()
            print(f"Assigned rfid_prefix {next_prefix} to organization '{org.name}'")
            next_prefix += 1
    else:
        print("All organizations already have rfid_prefix values")


def reverse_fix_rfid_prefix(apps, schema_editor):
    """Reverse operation - not needed for this fix"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0029_populate_existing_epcs'),
    ]

    operations = [
        # First, make the field nullable temporarily
        migrations.AlterField(
            model_name='organization',
            name='rfid_prefix',
            field=models.PositiveSmallIntegerField(
                blank=True,
                help_text='Numeric prefix for RFID EPCs (0-4095)',
                null=True,
                unique=True
            ),
        ),
        
        # Ensure all organizations have rfid_prefix values
        migrations.RunPython(
            ensure_all_orgs_have_rfid_prefix,
            reverse_fix_rfid_prefix
        ),
        
        # Make the field required again (but keep null=True for model compatibility)
        # The model will handle auto-assignment in the save method
    ]
