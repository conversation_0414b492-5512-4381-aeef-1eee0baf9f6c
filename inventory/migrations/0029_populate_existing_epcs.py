# Generated by Django 5.0 on 2025-06-19

from django.db import migrations


def populate_epcs(apps, schema_editor):
    """
    Populate EPC field for all existing items that don't have stored EPCs.
    Uses the same logic as the Item.generate_epc() method.
    """
    Item = apps.get_model('inventory', 'Item')
    
    def reverse_bits(value, bit_count):
        """Reverse the bits of a value within the specified bit count"""
        result = 0
        for i in range(bit_count):
            if value & (1 << i):
                result |= (1 << (bit_count - 1 - i))
        return result
    
    # Process items in batches to avoid memory issues
    batch_size = 1000
    updated_count = 0
    
    # Get items that need EPC population
    items_to_update = Item.objects.select_related('organization').filter(
        _epc__isnull=True,
        organization__isnull=False,
        organization__rfid_prefix__isnull=False,
        _item_code__isnull=False
    )
    
    total_items = items_to_update.count()
    print(f"Populating EPCs for {total_items} items...")
    
    for i in range(0, total_items, batch_size):
        batch = items_to_update[i:i + batch_size]
        
        for item in batch:
            try:
                # Generate EPC using the same logic as Item.generate_epc()
                org_prefix = item.organization.rfid_prefix & 0xFFF  # Ensure 12 bits
                org_prefix_reversed = reverse_bits(org_prefix, 12)
                
                # Get item code as integer (24 bits from 3 bytes)
                item_code_int = int.from_bytes(item._item_code, byteorder='big') & 0xFFFFFF
                
                # Padding is 60 bits of zeros
                padding = 0
                
                # Combine: [Org Prefix (reversed): 12 bits][Padding: 60 bits][Item Code: 24 bits]
                epc_int = (org_prefix_reversed << 84) | (padding << 24) | item_code_int
                
                # Convert to 12-byte binary representation
                item._epc = epc_int.to_bytes(12, byteorder='big')
                item.save(update_fields=['_epc'])
                
                updated_count += 1
                
            except Exception as e:
                print(f"Error generating EPC for item {item.pk}: {e}")
                continue
        
        print(f"Processed {min(i + batch_size, total_items)} of {total_items} items...")
    
    print(f"Successfully populated EPCs for {updated_count} items.")


def reverse_populate_epcs(apps, schema_editor):
    """
    Reverse migration - clear all stored EPCs.
    """
    Item = apps.get_model('inventory', 'Item')
    Item.objects.update(_epc=None)
    print("Cleared all stored EPCs.")


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0028_add_epc_storage'),
    ]

    operations = [
        migrations.RunPython(populate_epcs, reverse_populate_epcs),
    ]
