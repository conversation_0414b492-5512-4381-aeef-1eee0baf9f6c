# Generated by Django 5.0

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0017_add_rfid_prefix'),
    ]

    operations = [
        # First, rename the existing rfid_epc field to a temporary name
        migrations.RenameField(
            model_name='item',
            old_name='rfid_epc',
            new_name='rfid_epc_old',
        ),
        
        # Then add the new binary field
        migrations.AddField(
            model_name='item',
            name='_rfid_epc',
            field=models.BinaryField(
                blank=True,
                db_column='rfid_epc',
                editable=False,
                help_text='96-bit RFID Electronic Product Code',
                max_length=12,
                null=True,
                unique=True
            ),
        ),
    ]