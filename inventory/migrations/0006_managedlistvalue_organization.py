# Generated manually

import django.db.models.deletion
from django.db import migrations, models


def assign_default_organization_to_list_values(apps, schema_editor):
    ManagedListValue = apps.get_model('inventory', 'ManagedListValue')
    Organization = apps.get_model('inventory', 'Organization')
    
    default_org = Organization.objects.filter(code='DEFAULT').first()
    if default_org:
        ManagedListValue.objects.all().update(organization=default_org)


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0005_item_organization'),
    ]

    operations = [
        # First add the field as nullable
        migrations.AddField(
            model_name='managedlistvalue',
            name='organization',
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='list_values',
                to='inventory.organization'
            ),
        ),
        
        # Assign all existing list values to the default organization
        migrations.RunPython(
            assign_default_organization_to_list_values,
            migrations.RunPython.noop
        ),
        
        # Then make the field required
        migrations.AlterField(
            model_name='managedlistvalue',
            name='organization',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='list_values',
                to='inventory.organization'
            ),
        ),
        
        # Update unique constraint to include organization
        migrations.AlterUniqueTogether(
            name='managedlistvalue',
            unique_together={('organization', 'list_name', 'value')},
        ),
    ]