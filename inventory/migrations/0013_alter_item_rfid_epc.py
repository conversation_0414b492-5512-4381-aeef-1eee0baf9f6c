# Generated by Django 5.0 on 2025-05-16 02:20

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0012_item_rfid_epc'),
    ]

    operations = [
        migrations.AlterField(
            model_name='item',
            name='rfid_epc',
            field=models.CharField(blank=True, help_text='RFID Electronic Product Code (EPC)', max_length=32, null=True, unique=True, validators=[django.core.validators.RegexValidator(message='EPC must be 24-32 hexadecimal characters', regex='^[0-9A-Fa-f]{24,32}$')]),
        ),
    ]
