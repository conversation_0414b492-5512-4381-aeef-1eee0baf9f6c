# Generated manually

from django.db import migrations, models


def assign_unique_rfid_prefixes(apps, schema_editor):
    """Assign unique RFID prefixes to all organizations"""
    Organization = apps.get_model('inventory', 'Organization')

    # Assign sequential prefixes starting from 0
    for i, org in enumerate(Organization.objects.all().order_by('id')):
        org.rfid_prefix = i
        org.save()


def reverse_rfid_prefix_assignment(apps, schema_editor):
    """Remove all RFID prefixes"""
    Organization = apps.get_model('inventory', 'Organization')
    Organization.objects.all().update(rfid_prefix=None)


class Migration(migrations.Migration):
    dependencies = [
        ('inventory', '0026_make_rfid_prefix_required'),
    ]

    operations = [
        # Remove the old CharField
        migrations.RemoveField(
            model_name='organization',
            name='rfid_prefix',
        ),

        # Add the new PositiveSmallIntegerField (nullable initially)
        migrations.AddField(
            model_name='organization',
            name='rfid_prefix',
            field=models.PositiveSmallIntegerField(
                null=True,
                help_text="Numeric prefix for RFID EPCs (0-4095)"
            ),
        ),

        # Assign unique prefixes to all organizations
        migrations.RunPython(
            assign_unique_rfid_prefixes,
            reverse_rfid_prefix_assignment
        ),

        # Make the field required and unique
        migrations.AlterField(
            model_name='organization',
            name='rfid_prefix',
            field=models.PositiveSmallIntegerField(
                unique=True,
                help_text="Numeric prefix for RFID EPCs (0-4095)"
            ),
        ),
    ]