# Generated manually

import django.db.models.deletion
from django.db import migrations, models


def assign_default_organization(apps, schema_editor):
    Item = apps.get_model('inventory', 'Item')
    Organization = apps.get_model('inventory', 'Organization')
    
    default_org = Organization.objects.filter(code='DEFAULT').first()
    if default_org:
        Item.objects.all().update(organization=default_org)


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0004_create_default_organization'),
    ]

    operations = [
        # First add the field as nullable
        migrations.AddField(
            model_name='item',
            name='organization',
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='items',
                to='inventory.organization'
            ),
        ),
        
        # Assign all existing items to the default organization
        migrations.RunPython(
            assign_default_organization,
            migrations.RunPython.noop
        ),
        
        # Then make the field required
        migrations.AlterField(
            model_name='item',
            name='organization',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='items',
                to='inventory.organization'
            ),
        ),
    ]