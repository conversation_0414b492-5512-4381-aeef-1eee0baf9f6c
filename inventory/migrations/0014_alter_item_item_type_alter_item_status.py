# Generated by Django 5.0 on 2025-05-18 01:49

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0013_alter_item_rfid_epc'),
    ]

    operations = [
        migrations.AlterField(
            model_name='item',
            name='item_type',
            field=models.ForeignKey(blank=True, limit_choices_to={'is_active': True, 'list_name': 'ItemTypes'}, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='items_of_type', to='inventory.managedlistvalue'),
        ),
        migrations.AlterField(
            model_name='item',
            name='status',
            field=models.ForeignKey(blank=True, limit_choices_to={'is_active': True, 'list_name': 'ItemStatuses'}, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='items_with_status', to='inventory.managedlistvalue'),
        ),
    ]
