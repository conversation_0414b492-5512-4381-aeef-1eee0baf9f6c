# Generated by Django 5.0

from django.db import migrations


def assign_org_prefixes(apps, schema_editor):
    """Assign hierarchical prefixes to organizations"""
    Organization = apps.get_model('inventory', 'Organization')
    
    # Ensure all organizations have a prefix
    for org in Organization.objects.all():
        if not org.rfid_prefix:
            # Specific prefixes for existing organizations
            if org.pk == 2:
                org.rfid_prefix = "0"
            elif org.pk == 3:
                org.rfid_prefix = "1"
            else:
                # Generate a prefix based on ID for other organizations
                org_id = org.pk
                
                # Skip values already assigned to specific organizations
                if org_id > 3:
                    # Adjust to account for reserved prefixes
                    # We've used '0' and '1' for specific orgs
                    if 2 <= org_id <= 15:  # Single hex digit range
                        # Skip '0' and '1' which are already assigned
                        adjusted_id = org_id
                        if adjusted_id >= 2:  # Skip '0'
                            adjusted_id += 1
                        if adjusted_id >= 3:  # Skip '1'
                            adjusted_id += 1
                        org.rfid_prefix = format(adjusted_id - 2, 'x')
                    else:
                        # For larger IDs, use multi-digit hex
                        if org_id < 256:
                            # Second tier: two digits
                            org.rfid_prefix = format(org_id, '02x')
                        else:
                            # Third tier: three digits
                            org.rfid_prefix = format(org_id, '03x')
            org.save()


def reverse_org_prefixes(apps, schema_editor):
    """Reverse the prefix assignment (set all to null)"""
    Organization = apps.get_model('inventory', 'Organization')
    Organization.objects.all().update(rfid_prefix=None)


class Migration(migrations.Migration):
    dependencies = [
        ('inventory', '0024_add_rfid_prefix'),
    ]

    operations = [
        migrations.RunPython(assign_org_prefixes, reverse_org_prefixes),
    ]

from django.core.management.base import BaseCommand
from inventory.models import Organization

class Command(BaseCommand):
    help = 'Verifies and fixes RFID prefixes for organizations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Fix any missing or incorrect prefixes',
        )

    def handle(self, *args, **options):
        fix_mode = options['fix']
        orgs = Organization.objects.all()
        missing_count = 0
        
        for org in orgs:
            # Check for missing prefixes
            if org.rfid_prefix is None:
                missing_count += 1
                self.stdout.write(
                    self.style.WARNING(f'Organization {org.name} (ID: {org.pk}) is missing an RFID prefix')
                )
                if fix_mode:
                    org.assign_rfid_prefix()
                    org.save()
                    self.stdout.write(
                        self.style.SUCCESS(f'  - Assigned prefix: {org.rfid_prefix} (hex: {org.rfid_prefix_hex})')
                    )
        
        if missing_count == 0:
            self.stdout.write(
                self.style.SUCCESS('All organizations have RFID prefixes')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'Found {missing_count} organizations missing RFID prefixes')
            )
            if not fix_mode:
                self.stdout.write(
                    self.style.WARNING('Run with --fix to automatically assign prefixes')
                )
