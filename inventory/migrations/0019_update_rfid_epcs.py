# Generated by Django 5.0

from django.db import migrations


def update_rfid_epcs(apps, schema_editor):
    """Update RFID EPCs for all items based on organization prefix and item code"""
    Item = apps.get_model('inventory', 'Item')
    
    # Calculate EPCs for all items
    for item in Item.objects.select_related('organization').all():
        if item.organization and item._item_code:
            try:
                # Get organization prefix and pad to 3 digits
                prefix = item.organization.rfid_prefix.ljust(3, '0')
                
                # Get item code as hex
                item_code = item._item_code.hex()
                
                # Calculate padding length (24 hex chars total)
                padding_length = 24 - len(prefix) - len(item_code)
                padding = "0" * padding_length
                
                # Combine: [prefix][padding][item_code]
                full_epc_hex = prefix + padding + item_code
                
                # Convert to binary and store
                item._rfid_epc = bytes.fromhex(full_epc_hex)
                
                # Use raw SQL to update to avoid validation issues
                from django.db import connection
                with connection.cursor() as cursor:
                    cursor.execute(
                        "UPDATE inventory_item SET rfid_epc = %s WHERE id = %s",
                        [item._rfid_epc, item.pk]
                    )
            except Exception as e:
                print(f"Error updating EPC for item {item.pk}: {e}")


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0018_convert_rfid_epc_to_binary'),
    ]

    operations = [
        # Update all EPCs based on organization prefix and item code
        migrations.RunPython(update_rfid_epcs, migrations.RunPython.noop),
    ]
