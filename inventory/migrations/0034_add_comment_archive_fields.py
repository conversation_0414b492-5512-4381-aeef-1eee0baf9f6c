# Generated by Django 5.0 on 2025-06-19 19:37

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0033_alter_managedlistvalue_list_name_itemcomment_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='itemcomment',
            name='archived_at',
            field=models.DateTimeField(blank=True, help_text='Timestamp when comment was archived', null=True),
        ),
        migrations.AddField(
            model_name='itemcomment',
            name='archived_by',
            field=models.ForeignKey(blank=True, help_text='User who archived this comment', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='archived_comments', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='itemcomment',
            name='is_archived',
            field=models.BooleanField(default=False, help_text='Mark comment as archived/completed to hide from default views'),
        ),
        migrations.AddIndex(
            model_name='itemcomment',
            index=models.Index(fields=['is_archived'], name='inventory_i_is_arch_6646e8_idx'),
        ),
        migrations.AddIndex(
            model_name='itemcomment',
            index=models.Index(fields=['item', 'is_archived', '-created_at'], name='inventory_i_item_id_c7bd02_idx'),
        ),
    ]
