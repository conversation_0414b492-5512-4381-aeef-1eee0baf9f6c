# Generated by Django 5.0 on 2025-06-18 21:40

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0023_remove_epc_fields'),
    ]

    operations = [
        migrations.AddField(
            model_name='organization',
            name='rfid_prefix',
            field=models.CharField(blank=True, help_text='Hierarchical hex prefix for RFID EPCs (1-3 characters)', max_length=3, null=True, unique=True, validators=[django.core.validators.RegexValidator('^[0-9a-f]{1,3}$', 'Prefix must be 1-3 hex characters')]),
        ),
    ]
