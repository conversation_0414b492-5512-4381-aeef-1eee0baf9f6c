# Generated by Django 5.0 on 2025-05-08 04:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0001_initial'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='item',
            name='inventory_i_item_co_e61695_idx',
        ),
        migrations.AlterField(
            model_name='item',
            name='item_code',
            field=models.BinaryField(db_column='item_code', max_length=3, unique=True),
        ),
        migrations.RenameField(
            model_name='item',
            old_name='item_code',
            new_name='_item_code',
        ),
        migrations.AddIndex(
            model_name='item',
            index=models.Index(fields=['_item_code'], name='inventory_i_item_co_e61695_idx'),
        ),
    ]
