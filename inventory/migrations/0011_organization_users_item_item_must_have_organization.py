# Generated by Django 5.0 on 2025-05-16 01:49

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0010_create_default_organization_permissions'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='organization',
            name='users',
            field=models.ManyToManyField(related_name='organizations', through='inventory.OrganizationUser', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddConstraint(
            model_name='item',
            constraint=models.CheckConstraint(check=models.Q(('organization__isnull', False)), name='item_must_have_organization'),
        ),
    ]
