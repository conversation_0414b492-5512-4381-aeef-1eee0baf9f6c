# Generated by Django 5.0 on 2025-06-19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0027_replace_rfid_prefix_field'),
    ]

    operations = [
        # Add the _epc BinaryField to store pre-generated EPCs
        migrations.AddField(
            model_name='item',
            name='_epc',
            field=models.BinaryField(
                blank=True,
                db_column='epc',
                editable=False,
                help_text='96-bit Electronic Product Code stored as binary',
                max_length=12,  # 96 bits = 12 bytes
                null=True,
                unique=True
            ),
        ),
        
        # Add database index for EPC search performance
        migrations.AddIndex(
            model_name='item',
            index=models.Index(fields=['_epc'], name='inventory_item_epc_idx'),
        ),
    ]
