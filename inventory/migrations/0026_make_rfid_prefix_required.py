# Generated by Django 5.0

from django.db import migrations, models
from django.core.validators import RegexValidator


class Migration(migrations.Migration):
    dependencies = [
        ('inventory', '0025_populate_rfid_prefixes'),
    ]

    operations = [
        migrations.AlterField(
            model_name='organization',
            name='rfid_prefix',
            field=models.CharField(
                max_length=3,
                unique=True,
                validators=[
                    RegexValidator(
                        r'^[0-9a-f]{1,3}$', 
                        'Prefix must be 1-3 hex characters'
                    )
                ],
                help_text="Hierarchical hex prefix for RFID EPCs (1-3 characters)"
            ),
        ),
    ]