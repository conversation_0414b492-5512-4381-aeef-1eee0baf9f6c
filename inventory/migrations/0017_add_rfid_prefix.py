# Generated by Django 5.0

from django.db import migrations, models
import django.core.validators


def assign_org_prefixes(apps, schema_editor):
    """Assign hierarchical prefixes to organizations"""
    Organization = apps.get_model('inventory', 'Organization')
    
    # Ensure all organizations have a prefix
    for org in Organization.objects.all():
        if not org.rfid_prefix:
            # Default prefix for existing organization with ID 2
            if org.pk == 2:  # Your first existing org has ID 2
                org.rfid_prefix = "00"
            else:
                # Generate a prefix based on ID, but skip "00" which is reserved for org ID 2
                org_id = org.pk
                if org.pk > 2:
                    org_id = org.pk - 1  # Adjust to account for reserved "00"
                
                if org_id < 16:
                    # First tier: single digit
                    org.rfid_prefix = format(org_id, 'x')
                elif org_id < 256:
                    # Second tier: two digits
                    org.rfid_prefix = format(org_id, '02x')
                else:
                    # Third tier: three digits
                    org.rfid_prefix = format(org_id, '03x')
            org.save()


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0016_item_image_item_image_caption'),
    ]

    operations = [
        # 1. Add rfid_prefix to Organization
        migrations.AddField(
            model_name='organization',
            name='rfid_prefix',
            field=models.CharField(
                max_length=3,
                null=True,
                blank=True,
                unique=True,
                validators=[django.core.validators.RegexValidator(r'^[0-9a-f]{1,3}$', 'Prefix must be 1-3 hex characters')],
                help_text="Hierarchical hex prefix for RFID EPCs (1-3 characters)"
            ),
        ),
        
        # 2. Assign prefixes to organizations
        migrations.RunPython(assign_org_prefixes),
        
        # 3. Make rfid_prefix required after populating
        migrations.AlterField(
            model_name='organization',
            name='rfid_prefix',
            field=models.CharField(
                max_length=3,
                unique=True,
                validators=[django.core.validators.RegexValidator(r'^[0-9a-f]{1,3}$', 'Prefix must be 1-3 hex characters')],
                help_text="Hierarchical hex prefix for RFID EPCs (1-3 characters)"
            ),
        ),
    ]