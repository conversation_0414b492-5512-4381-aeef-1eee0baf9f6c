from django.shortcuts import redirect
from django.contrib import messages
from django.urls import reverse
import logging

logger = logging.getLogger(__name__)

class OrganizationMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Skip for static and media URLs
        if request.path.startswith('/static/') or request.path.startswith('/media/'):
            return self.get_response(request)
            
        # Skip for admin URLs
        if request.path.startswith('/admin/'):
            return self.get_response(request)
            
        # Skip for authentication URLs
        if request.path.startswith('/accounts/'):
            return self.get_response(request)
            
        # Skip for debug and test URLs
        if request.path.startswith('/inventory/debug-session/') or request.path.startswith('/inventory/test-session/'):
            return self.get_response(request)
            
        # Skip for organization selection and no-organization URLs
        if request.path == reverse('inventory:select_organization') or request.path == reverse('inventory:no_organizations'):
            return self.get_response(request)

        # Skip for organization switching URL (both GET and POST)
        switch_org_base_url = '/inventory/organization/switch/'
        if request.path.startswith(switch_org_base_url):
            logger.debug(f"Skipping middleware for organization switching URL: {request.path}")
            return self.get_response(request)

        # Skip for logout URL
        if request.path == reverse('inventory:logout'):
            logger.debug(f"Skipping middleware for logout URL: {request.path}")
            return self.get_response(request)
            
        # Get org_id from session
        org_id = request.session.get('org_id')

        # If no org_id in session, check for backup cookie
        if not org_id and 'backup_org_id' in request.COOKIES:
            org_id = request.COOKIES.get('backup_org_id')
            # Store it back in the session
            request.session['org_id'] = org_id
            request.session.modified = True
            request.session.save()
            logger.debug(f"Restored org_id={org_id} from backup cookie")

        # If still no org_id, check localStorage via JavaScript (will be handled in templates)

        # Log the session state for debugging
        logger.debug(f"Middleware processing path: {request.path}")
        logger.debug(f"Session in middleware: {dict(request.session)}")
        logger.debug(f"Session key: {request.session.session_key}")
        logger.debug(f"org_id from session: {org_id}")
        logger.debug(f"User: {request.user.username if request.user.is_authenticated else 'Anonymous'}")

        # Store org_id in request for easy access in views
        request.org_id = org_id
        
        # If user is authenticated but no organization is selected
        if request.user.is_authenticated and not org_id:
            # Get available organizations for this user
            from .models import Organization, OrganizationUser
            
            # Get organizations where user is a member
            user_orgs = Organization.objects.filter(
                organizationuser__user=request.user,
                is_active=True
            ) if not request.user.is_superuser else Organization.objects.filter(is_active=True)
            
            if user_orgs.count() == 1:
                # If only one organization exists, select it automatically
                request.session['org_id'] = str(user_orgs.first().id)
                request.session.modified = True
                request.session.save()
                logger.debug(f"Auto-selected organization {user_orgs.first().name} for user {request.user.username}")
                logger.debug(f"Session after auto-select: {dict(request.session)}")
            elif user_orgs.exists() and not request.path == reverse('inventory:select_organization'):
                # If multiple orgs exist but none selected, redirect to selection page
                logger.debug(f"Redirecting user {request.user.username} to organization selection")
                messages.info(request, "Please select an organization to continue")
                return redirect('inventory:select_organization')
            elif not user_orgs.exists() and not request.path == reverse('inventory:no_organizations'):
                # If user has no organizations, redirect to a page explaining this
                logger.debug(f"User {request.user.username} has no organizations, redirecting to no_organizations")
                return redirect('inventory:no_organizations')
        
        # Check if user has access to the selected organization
        if request.user.is_authenticated and org_id and not request.path.startswith('/accounts/'):
            from .models import OrganizationUser
            
            # Superusers have access to all organizations
            if request.user.is_superuser:
                logger.debug(f"Allowing superuser {request.user.username} access to organization {org_id}")
                return self.get_response(request)
            
            # Check if user is a member of the selected organization
            has_access = OrganizationUser.objects.filter(
                user=request.user,
                organization_id=org_id
            ).exists()
            
            if not has_access:
                # If user doesn't have access to this organization, clear the selection
                logger.warning(f"User {request.user.username} doesn't have access to organization {org_id}")
                del request.session['org_id']
                messages.warning(request, "You don't have access to that organization")
                return redirect('inventory:select_organization')
        
        response = self.get_response(request)
        return response
