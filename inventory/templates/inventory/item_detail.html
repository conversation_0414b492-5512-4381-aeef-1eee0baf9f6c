{% extends 'base.html' %}
{% load inventory_filters %}

{% block title %}{{ item.item_name }} - Inventory Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{{ item.item_name }}</h1>
    <div class="btn-group" role="group">
        <a href="{% url 'inventory:edit_item_by_code' item_code=item.item_code %}" class="btn btn-primary">Edit Item</a>
        {% if not item.is_archived %}
        <a href="{% url 'inventory:archive_item_by_code' item_code=item.item_code %}" class="btn btn-outline-danger">Archive Item</a>
        {% endif %}
    </div>
</div>

<div class="row">
    <div class="col-md-9">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">Item Details</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-4">
                        <h6>Item Code</h6>
                        <div class="code-box">{{ item.item_code }}</div>
                    </div>
                    <div class="col-md-4">
                        <h6>EPC (Electronic Product Code)</h6>
                        {% if item.epc %}
                            <div class="epc-barcode-container mb-3 text-center">
                                <div class="barcode-wrapper d-inline-block" style="background: white; border: 1px solid #dee2e6; border-radius: 4px; padding: 12px;">
                                    <img src="{% url 'inventory:item_barcode' item_code=item.item_code %}?format=auto&size=150"
                                         alt="EPC 2D Barcode: {{ item.epc }}"
                                         class="epc-barcode"
                                         style="width: 150px; height: 150px; object-fit: contain;"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';"
                                         title="2D Barcode - {{ item.epc }}">
                                </div>
                                <div class="barcode-info mt-2">
                                    <small class="text-muted d-block">2D Barcode (DataMatrix/QR)</small>
                                </div>
                            </div>
                        {% else %}
                            <p class="text-muted">EPC not available</p>
                        {% endif %}
                    </div>
                    <div class="col-md-4">
                        <h6>Location</h6>
                        <p>
                            {% if item.located_in %}
                                <a href="{% url 'inventory:item_detail' pk=item.located_in.pk %}">
                                    {{ item.located_in.item_name }} ({{ item.located_in.item_code }})
                                </a>
                                <a href="{% url 'inventory:move_item' pk=item.pk %}" class="btn btn-sm btn-outline-secondary ms-2">
                                    <i class="fas fa-exchange-alt"></i> Move
                                </a>
                            {% else %}
                                <span class="text-muted">Top Level (Not in any container)</span>
                                <a href="{% url 'inventory:move_item' pk=item.pk %}" class="btn btn-sm btn-outline-secondary ms-2">
                                    <i class="fas fa-exchange-alt"></i> Move
                                </a>
                            {% endif %}
                        </p>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <h6>Item Type</h6>
                        <p>{{ item.item_type.value }}</p>
                    </div>
                    <div class="col-md-4">
                        <h6>Status</h6>
                        <p>{{ item.status.value }}</p>
                    </div>
                    <div class="col-md-4">
                        <h6>Last Updated</h6>
                        <p>{{ item.last_updated|date:"F j, Y, P" }}</p>
                    </div>
                </div>

                <h6>Description</h6>
                <p>{{ item.item_description|default:"No description provided." }}</p>

                </div>

                {% if item.custom_fields %}
                <h6>Custom Fields</h6>
                <table class="table table-sm">
                    <tbody>
                        {% for key, value in item.custom_fields.items %}
                        <tr>
                            <th scope="row" style="width: 30%;">{{ key }}</th>
                            <td>{{ value }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% endif %}

                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>Date Added</h6>
                        <p>{{ item.date_added|date:"F j, Y, P" }}</p>
                    </div>
                </div>

                {% if item.is_archived %}
                <div class="alert alert-warning mt-3">
                    <strong>Archived Item</strong> - This item has been archived.
                </div>
                {% endif %}
            </div>
        </div>

        {% if contained_items %}
        <div class="card mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Contained Items ({{ contained_items.count }})</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for contained_item in contained_items %}
                            <tr>
                                <td><code>{{ contained_item.item_code }}</code></td>
                                <td>{{ contained_item.item_name }}</td>
                                <td>{{ contained_item.item_type.value }}</td>
                                <td>{{ contained_item.status.value }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'inventory:item_detail' pk=contained_item.pk %}" class="btn btn-outline-primary">View</a>
                                        <a href="{% url 'inventory:move_item' pk=contained_item.pk %}" class="btn btn-outline-secondary">Move</a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Comments Section -->
        <div class="card mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-comments me-2"></i>Comments
                    {% if recent_comments %}
                        <span class="badge bg-secondary">{{ recent_comments|length }}</span>
                    {% endif %}
                </h5>
                <a href="{% url 'inventory:recent_comments' %}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-list me-1"></i>All Recent Comments
                </a>
            </div>
            <div class="card-body">
                <!-- Comment Form -->
                <form method="post" action="{% url 'inventory:add_comment' item_code=item.item_code %}" enctype="multipart/form-data" class="mb-4">
                    {% csrf_token %}

                    <!-- Guest name field (shown only for non-authenticated users) -->
                    {% if not user.is_authenticated %}
                    <div class="mb-3">
                        <label for="{{ comment_form.guest_name.id_for_label }}" class="form-label">
                            <i class="fas fa-user me-1"></i>Your Name <span class="text-danger">*</span>
                        </label>
                        {{ comment_form.guest_name }}
                        {% if comment_form.guest_name.errors %}
                            <div class="text-danger small">{{ comment_form.guest_name.errors.0 }}</div>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- Comment text -->
                    <div class="mb-3">
                        <label for="{{ comment_form.comment_text.id_for_label }}" class="form-label">
                            <i class="fas fa-comment me-1"></i>Comment <span class="text-danger">*</span>
                        </label>
                        {{ comment_form.comment_text }}
                        {% if comment_form.comment_text.errors %}
                            <div class="text-danger small">{{ comment_form.comment_text.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <!-- Reason dropdown -->
                    <div class="mb-3">
                        <label for="{{ comment_form.reason.id_for_label }}" class="form-label">
                            <i class="fas fa-tag me-1"></i>Reason (Optional)
                        </label>
                        {{ comment_form.reason }}
                        {% if comment_form.reason.errors %}
                            <div class="text-danger small">{{ comment_form.reason.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <!-- Photo upload -->
                    <div class="mb-3">
                        <label for="{{ comment_form.photo.id_for_label }}" class="form-label">
                            <i class="fas fa-camera me-1"></i>Photo (Optional)
                        </label>
                        {{ comment_form.photo }}
                        <div class="form-text">JPG files only, max 10MB</div>
                        {% if comment_form.photo.errors %}
                            <div class="text-danger small">{{ comment_form.photo.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Add Comment
                    </button>
                </form>

                <!-- Existing Comments -->
                {% if recent_comments %}
                    <hr>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">Recent Comments (showing {{ recent_comments|length }} of {{ item.comments.count }})</h6>

                        <!-- Archive Toggle for Authenticated Users -->
                        {% if user.is_authenticated %}
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="showArchivedToggle"
                                   {% if show_archived %}checked{% endif %}
                                   onchange="toggleArchivedComments(this.checked)">
                            <label class="form-check-label" for="showArchivedToggle">
                                <small>Show Archived</small>
                            </label>
                        </div>
                        {% endif %}
                    </div>

                    {% for comment in recent_comments %}
                    <div class="comment-item border-bottom pb-3 mb-3 {% if comment.is_archived %}archived-comment{% endif %}">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div class="d-flex align-items-center">
                                {% if comment.is_guest_comment %}
                                    <i class="fas fa-user-circle text-muted me-2" title="Guest User"></i>
                                    <strong>{{ comment.commenter_name }}</strong>
                                    <span class="badge bg-light text-dark ms-2">Guest</span>
                                {% else %}
                                    <i class="fas fa-user-check text-primary me-2" title="Authenticated User"></i>
                                    <strong>{{ comment.commenter_name }}</strong>
                                    <span class="badge bg-primary ms-2">User</span>
                                {% endif %}

                                {% if comment.reason %}
                                    <span class="badge bg-warning text-dark ms-2">
                                        <i class="fas fa-tag me-1"></i>{{ comment.reason.value }}
                                    </span>
                                {% endif %}

                                <!-- Archive status badge -->
                                {% if comment.is_archived %}
                                    <span class="badge bg-secondary ms-2" title="{{ comment.archive_status_display }}">
                                        <i class="fas fa-archive me-1"></i>Archived
                                    </span>
                                {% endif %}
                            </div>

                            <div class="text-muted small d-flex align-items-center">
                                <span class="me-2">{{ comment.created_at|date:"M j, Y g:i A" }}</span>
                                {% if comment.is_edited %}
                                    <span class="text-muted me-2">(edited)</span>
                                {% endif %}

                                <!-- Action buttons for authenticated users -->
                                {% if user.is_authenticated %}
                                    <div class="btn-group btn-group-sm" role="group">
                                        <!-- Edit button (only for comment owner) -->
                                        {% if comment.user == user %}
                                            <a href="{% url 'inventory:edit_comment' comment_id=comment.id %}"
                                               class="btn btn-outline-secondary" title="Edit Comment">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        {% endif %}

                                        <!-- Archive/Unarchive button -->
                                        {% if comment.is_archived %}
                                            <form method="post" action="{% url 'inventory:unarchive_comment' comment_id=comment.id %}" class="d-inline">
                                                {% csrf_token %}
                                                <input type="hidden" name="next" value="{{ request.get_full_path }}">
                                                <button type="submit" class="btn btn-outline-success" title="Unarchive Comment">
                                                    <i class="fas fa-undo"></i>
                                                </button>
                                            </form>
                                        {% else %}
                                            <form method="post" action="{% url 'inventory:archive_comment' comment_id=comment.id %}" class="d-inline">
                                                {% csrf_token %}
                                                <input type="hidden" name="next" value="{{ request.get_full_path }}">
                                                <button type="submit" class="btn btn-outline-warning" title="Archive Comment">
                                                    <i class="fas fa-archive"></i>
                                                </button>
                                            </form>
                                        {% endif %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="comment-text">
                            {{ comment.comment_text|linebreaks }}
                        </div>

                        {% if comment.photo %}
                        <div class="comment-photo mt-2">
                            <a href="{{ comment.photo.url }}" target="_blank">
                                <img src="{{ comment.photo.url }}" alt="Comment photo"
                                     class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                            </a>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}

                    {% if item.comments.count > 10 %}
                    <div class="text-center">
                        <p class="text-muted">Showing 10 most recent comments out of {{ item.comments.count }} total.</p>
                        <a href="{% url 'inventory:recent_comments' %}?item={{ item.item_code }}" class="btn btn-outline-primary">
                            View All Comments for This Item
                        </a>
                    </div>
                    {% endif %}
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-comments fa-3x mb-3 opacity-50"></i>
                        <p>No comments yet. Be the first to add one!</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-3">
        {% if item.image %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">Image</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <a href="{{ item.image.url }}" target="_blank">
                        <img src="{{ item.image.url }}" alt="{{ item.image_caption|default:item.item_name }}"
                             class="img-fluid rounded">
                    </a>
                    {% if item.image_caption %}
                    <p class="text-center mt-1"><small>{{ item.image_caption }}</small></p>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .archived-comment {
        opacity: 0.7;
        background-color: #f8f9fa;
        border-left: 3px solid #6c757d;
        padding-left: 10px;
    }

    .comment-item {
        transition: opacity 0.3s ease;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
function toggleArchivedComments(showArchived) {
    const currentUrl = new URL(window.location);
    if (showArchived) {
        currentUrl.searchParams.set('show_archived', 'true');
    } else {
        currentUrl.searchParams.delete('show_archived');
    }
    window.location.href = currentUrl.toString();
}
</script>
{% endblock %}
