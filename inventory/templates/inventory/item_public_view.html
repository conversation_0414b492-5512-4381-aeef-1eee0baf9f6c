{% extends 'base_minimal.html' %}
{% load inventory_filters %}

{% block title %}{{ item.item_name }} - Public View{% endblock %}

{% block content %}
<div class="container">
    {% if item.organization %}
        <div class="text-center mb-4">
            {% if item.organization.logo %}
                <img src="{{ item.organization.logo.url }}" alt="{{ item.organization.name }}" style="max-height: 80px;" class="mb-3">
            {% endif %}
            <h4>{{ item.organization.name }} Inventory</h4>
        </div>
        
        <style>
            :root {
                --primary: {{ item.organization.primary_color }};
                --secondary: {{ item.organization.secondary_color }};
                --bs-primary: {{ item.organization.primary_color }};
                --bs-primary-rgb: {{ item.organization.primary_color|hex_to_rgb }};
                --bs-secondary: {{ item.organization.secondary_color }};
                --bs-secondary-rgb: {{ item.organization.secondary_color|hex_to_rgb }};
            }
            
            .card {
                border-left: 3px solid var(--primary);
            }
            
            .code-box { 
                padding: 10px; 
                border: 1px solid #ddd; 
                display: inline-block;
                font-family: monospace;
                font-size: 1.2em;
                margin-bottom: 10px;
                background-color: #f8f9fa;
            }
            
            .status-badge {
                display: inline-block;
                padding: 0.25rem 0.5rem;
                font-size: 0.875rem;
                font-weight: 500;
                border-radius: 0.25rem;
                background-color: var(--secondary);
                color: white;
                margin-left: 0.5rem;
            }
        </style>
    {% endif %}

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ item.item_name }}</h5>
            {% if item.status %}
            <span class="status-badge">{{ item.status.value }}</span>
            {% endif %}
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <h6>Item Code</h6>
                    <div class="code-box">{{ item.item_code }}</div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-12">
                    <h6>Location</h6>
                    <p>
                        <span class="text-muted">Location is not available</span>
                    </p>
                </div>
            </div>
            
            {% if item.item_description %}
            <div class="mb-3">
                <h6>Description</h6>
                <p>{{ item.item_description }}</p>
            </div>
            {% endif %}
            
            {% if item.custom_fields %}
            <h6>Custom Fields</h6>
            <table class="table table-sm">
                <tbody>
                    {% for key, value in item.custom_fields.items %}
                    <tr>
                        <th scope="row" style="width: 30%;">{{ key }}</th>
                        <td>{{ value }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
        </div>
    </div>

    <!-- Comments Section for Public View -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-comments me-2"></i>Comments
                {% if recent_comments %}
                    <span class="badge bg-secondary">{{ recent_comments|length }}</span>
                {% endif %}
            </h5>
        </div>
        <div class="card-body">
            <!-- Comment Form for Guests -->
            <form method="post" action="{% url 'inventory:add_comment' item_code=item.item_code %}" enctype="multipart/form-data" class="mb-4">
                {% csrf_token %}

                <!-- Guest name field -->
                <div class="mb-3">
                    <label for="{{ comment_form.guest_name.id_for_label }}" class="form-label">
                        <i class="fas fa-user me-1"></i>Your Name <span class="text-danger">*</span>
                    </label>
                    {{ comment_form.guest_name }}
                    {% if comment_form.guest_name.errors %}
                        <div class="text-danger small">{{ comment_form.guest_name.errors.0 }}</div>
                    {% endif %}
                </div>

                <!-- Comment text -->
                <div class="mb-3">
                    <label for="{{ comment_form.comment_text.id_for_label }}" class="form-label">
                        <i class="fas fa-comment me-1"></i>Comment <span class="text-danger">*</span>
                    </label>
                    {{ comment_form.comment_text }}
                    {% if comment_form.comment_text.errors %}
                        <div class="text-danger small">{{ comment_form.comment_text.errors.0 }}</div>
                    {% endif %}
                </div>

                <!-- Reason dropdown -->
                <div class="mb-3">
                    <label for="{{ comment_form.reason.id_for_label }}" class="form-label">
                        <i class="fas fa-tag me-1"></i>Reason (Optional)
                    </label>
                    {{ comment_form.reason }}
                    {% if comment_form.reason.errors %}
                        <div class="text-danger small">{{ comment_form.reason.errors.0 }}</div>
                    {% endif %}
                </div>

                <!-- Photo upload -->
                <div class="mb-3">
                    <label for="{{ comment_form.photo.id_for_label }}" class="form-label">
                        <i class="fas fa-camera me-1"></i>Photo (Optional)
                    </label>
                    {{ comment_form.photo }}
                    <div class="form-text">JPG files only, max 10MB</div>
                    {% if comment_form.photo.errors %}
                        <div class="text-danger small">{{ comment_form.photo.errors.0 }}</div>
                    {% endif %}
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>Add Comment
                </button>
            </form>

            <!-- Existing Comments -->
            {% if recent_comments %}
                <hr>
                <h6 class="mb-3">Recent Comments</h6>

                {% for comment in recent_comments %}
                <div class="comment-item border-bottom pb-3 mb-3 {% if comment.is_archived %}archived-comment{% endif %}">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div class="d-flex align-items-center">
                            {% if comment.is_guest_comment %}
                                <i class="fas fa-user-circle text-muted me-2" title="Guest User"></i>
                                <strong>{{ comment.commenter_name }}</strong>
                                <span class="badge bg-light text-dark ms-2">Guest</span>
                            {% else %}
                                <i class="fas fa-user-check text-primary me-2" title="Authenticated User"></i>
                                <strong>{{ comment.commenter_name }}</strong>
                                <span class="badge bg-primary ms-2">User</span>
                            {% endif %}

                            {% if comment.reason %}
                                <span class="badge bg-warning text-dark ms-2">
                                    <i class="fas fa-tag me-1"></i>{{ comment.reason.value }}
                                </span>
                            {% endif %}

                            <!-- Archive status badge -->
                            {% if comment.is_archived %}
                                <span class="badge bg-secondary ms-2" title="{{ comment.archive_status_display }}">
                                    <i class="fas fa-archive me-1"></i>Archived
                                </span>
                            {% endif %}
                        </div>

                        <div class="text-muted small">
                            {{ comment.created_at|date:"M j, Y g:i A" }}
                            {% if comment.is_edited %}
                                <span class="text-muted">(edited)</span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="comment-text">
                        {{ comment.comment_text|linebreaks }}
                    </div>

                    {% if comment.photo %}
                    <div class="comment-photo mt-2">
                        <a href="{{ comment.photo.url }}" target="_blank">
                            <img src="{{ comment.photo.url }}" alt="Comment photo"
                                 class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                        </a>
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-comments fa-3x mb-3 opacity-50"></i>
                    <p>No comments yet. Be the first to add one!</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
