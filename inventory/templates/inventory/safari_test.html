{% extends 'base.html' %}
{% load static %}

{% block title %}Safari CSS Test - Inventory Management{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">

<!-- Include inline CSS first to ensure styles are applied immediately -->
{% include 'inventory/inline_css.html' %}

<!-- Primary CSS link with cache-busting parameter -->
<link rel="stylesheet" type="text/css" href="{% static 'css/item_code_lookup.css' %}?v={% now 'U' %}" id="item-code-lookup-css">
<style>
    .test-container {
        margin: 2rem 0;
        padding: 1rem;
        border: 1px solid #ddd;
        border-radius: 0.25rem;
    }

    .debug-info {
        margin-top: 1rem;
        padding: 1rem;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
    }

    .test-element {
        margin: 1rem 0;
        padding: 1rem;
        border: 1px solid #ddd;
    }

    .ui-autocomplete-test {
        background-color: #ffffff;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        padding: 0.5rem 0;
        margin-top: 2px;
        width: 100%;
        max-height: 300px;
        overflow-y: auto;
        overflow-x: hidden;
        z-index: 1050;
    }

    .test-success {
        color: #28a745;
        font-weight: bold;
    }

    .test-failure {
        color: #dc3545;
        font-weight: bold;
    }

    .test-warning {
        color: #ffc107;
        font-weight: bold;
    }

    .test-info {
        color: #17a2b8;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Safari CSS Test</h1>
    <div>
        <a href="{% url 'inventory:test_location_picker' %}" class="btn btn-outline-secondary me-2">
            Test Location Picker
        </a>
        <a href="{% url 'inventory:test_static_file' %}" class="btn btn-outline-secondary">
            Test Static Files
        </a>
    </div>
</div>

<div class="alert alert-info">
    <h4 class="alert-heading">Safari-Specific CSS Troubleshooting</h4>
    <p>This page is designed to help diagnose CSS loading issues specifically in Safari.</p>
    <hr>
    <p class="mb-0">Follow the steps below to troubleshoot CSS loading issues in Safari.</p>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title">CSS Loading Test</h5>
    </div>
    <div class="card-body">
        <div id="css-loading-status">Checking CSS loading status...</div>

        <div class="mt-3">
            <button id="test-css-btn" class="btn btn-primary">Check CSS Status</button>
            <button id="force-reload-btn" class="btn btn-warning ms-2">Show Reload Instructions</button>
        </div>

        <div class="debug-info mt-3">
            <h6>CSS File Information:</h6>
            <div id="css-info"></div>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title">Visual Style Test</h5>
    </div>
    <div class="card-body">
        <p>The element below should have the following styles if CSS is loaded correctly:</p>
        <ul>
            <li>Background color: white</li>
            <li>Border: 1px solid #ced4da</li>
            <li>Border radius: 0.25rem</li>
            <li>Box shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15)</li>
        </ul>

        <div class="test-element ui-autocomplete" id="test-element">
            This is a test element with the class "ui-autocomplete"
        </div>

        <div class="mt-3">
            <button id="check-styles-btn" class="btn btn-primary">Check Applied Styles</button>
        </div>

        <div class="debug-info mt-3">
            <h6>Applied Styles:</h6>
            <div id="style-info"></div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title">Safari Troubleshooting Steps</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <p><strong>Note:</strong> This page uses two approaches to ensure styles are applied correctly:</p>
            <ol>
                <li>Inline CSS is included directly in the HTML</li>
                <li>External CSS file is loaded with proper cache-busting</li>
            </ol>
            <p>Even if the external CSS file fails to load, the inline CSS should ensure basic styling works.</p>
        </div>

        <h5>If you're still experiencing styling issues:</h5>
        <ol>
            <li>
                <strong>Clear Safari's Cache:</strong>
                <ul>
                    <li>Go to Safari > Settings > Advanced</li>
                    <li>Check "Show Develop menu in menu bar"</li>
                    <li>Use Develop > Empty Caches (⌥⌘E)</li>
                </ul>
            </li>
            <li>
                <strong>Disable Safari's Cache During Development:</strong>
                <ul>
                    <li>With Developer Tools open, go to Develop > Disable Caches</li>
                </ul>
            </li>
            <li>
                <strong>Check Network Requests:</strong>
                <ul>
                    <li>Open Web Inspector (⌥⌘I)</li>
                    <li>Go to the Network tab</li>
                    <li>Reload the page (⌘R)</li>
                    <li>Look for item_code_lookup.css in the resources list</li>
                    <li>Check the HTTP status code (should be 200)</li>
                    <li>Check the Content-Type header (should be text/css)</li>
                </ul>
            </li>
            <li>
                <strong>Try Direct CSS Link:</strong>
                <ul>
                    <li><a href="{% static 'css/item_code_lookup.css' %}?v={% now 'U' %}" target="_blank">Open CSS file directly</a></li>
                </ul>
            </li>
            <li>
                <strong>Check for Content Blockers:</strong>
                <ul>
                    <li>Go to Safari > Settings > Extensions</li>
                    <li>Look for any content blockers that might be affecting CSS loading</li>
                    <li>Try temporarily disabling extensions</li>
                </ul>
            </li>
        </ol>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check if jQuery is loaded
        if (typeof jQuery === 'undefined') {
            console.error('jQuery is not loaded');
            document.getElementById('css-loading-status').innerHTML =
                '<div class="alert alert-danger">Error: jQuery is not loaded</div>';
            return;
        }

        // Check if CSS is loaded
        checkCSSLoading();

        // Test CSS loading button
        document.getElementById('test-css-btn').addEventListener('click', function() {
            checkCSSLoading();
        });

        // Force CSS reload button
        document.getElementById('force-reload-btn').addEventListener('click', function() {
            forceCSSReload();
        });

        // Check styles button
        document.getElementById('check-styles-btn').addEventListener('click', function() {
            checkAppliedStyles();
        });

        // Initial style check
        checkAppliedStyles();
    });

    function checkCSSLoading() {
        const cssLink = document.getElementById('item-code-lookup-css');
        const cssLoadingStatus = document.getElementById('css-loading-status');
        const cssInfo = document.getElementById('css-info');

        // Check if the CSS link element exists
        if (!cssLink) {
            // Provide more detailed information about why the CSS link might not be found
            let allLinks = document.querySelectorAll('link');
            let linkInfo = '<ul>';

            if (allLinks.length === 0) {
                linkInfo += '<li>No link elements found in the document</li>';
            } else {
                linkInfo += `<li>Found ${allLinks.length} link elements, but none with id="item-code-lookup-css":</li>`;
                allLinks.forEach((link, index) => {
                    linkInfo += `<li>Link ${index + 1}: rel="${link.rel}", href="${link.href}"${link.id ? `, id="${link.id}"` : ''}</li>`;
                });
            }

            linkInfo += '</ul>';

            cssLoadingStatus.innerHTML =
                '<div class="alert alert-danger">Error: CSS link element not found in the DOM</div>';

            cssInfo.innerHTML = `
                <div class="alert alert-warning">
                    <h5>Debug Information:</h5>
                    <p>The CSS link with id="item-code-lookup-css" was not found in the DOM.</p>
                    <p>This could be because:</p>
                    <ul>
                        <li>The extra_css block is not being included in the base template</li>
                        <li>The CSS link is being removed by JavaScript</li>
                        <li>The CSS link has a different ID</li>
                    </ul>
                    <h6>All link elements in the document:</h6>
                    ${linkInfo}
                </div>
            `;
            return;
        }

        // Get CSS link information
        const cssUrl = cssLink.getAttribute('href');

        // Check if CSS is loaded using styleSheets
        let cssLoaded = false;
        let cssInfo_html = '<ul>';

        // Check if inline CSS is applied
        cssInfo_html += '<li>Inline CSS is included as a fallback</li>';
        cssInfo_html += `<li>CSS link found: id="${cssLink.id}", href="${cssUrl}"</li>`;

        for (let i = 0; i < document.styleSheets.length; i++) {
            try {
                const sheet = document.styleSheets[i];
                if (sheet.href && sheet.href.includes('item_code_lookup.css')) {
                    cssLoaded = true;
                    cssInfo_html += `<li class="test-success">CSS file found in styleSheets[${i}]</li>`;
                    cssInfo_html += `<li>href: ${sheet.href}</li>`;

                    // Try to access rules to check if the CSS is actually loaded
                    try {
                        const rules = sheet.cssRules || sheet.rules;
                        cssInfo_html += `<li>Rules count: ${rules.length}</li>`;

                        // Check for specific rules
                        let uiAutocompleteFound = false;
                        for (let j = 0; j < rules.length; j++) {
                            if (rules[j].selectorText && rules[j].selectorText.includes('.ui-autocomplete')) {
                                uiAutocompleteFound = true;
                                cssInfo_html += `<li class="test-success">Found .ui-autocomplete rule at index ${j}</li>`;
                                break;
                            }
                        }

                        if (!uiAutocompleteFound) {
                            cssInfo_html += `<li class="test-warning">Could not find .ui-autocomplete rule</li>`;
                        }
                    } catch (e) {
                        cssInfo_html += `<li class="test-failure">Error accessing rules: ${e.message}</li>`;
                        cssInfo_html += `<li class="test-warning">This is likely due to CORS restrictions when the stylesheet is from a different domain</li>`;
                    }

                    break;
                }
            } catch (e) {
                cssInfo_html += `<li class="test-warning">Error checking styleSheet[${i}]: ${e.message}</li>`;
            }
        }

        cssInfo_html += '</ul>';
        cssInfo.innerHTML = cssInfo_html;

        if (cssLoaded) {
            cssLoadingStatus.innerHTML =
                '<div class="alert alert-success">CSS file appears to be loaded</div>';
        } else {
            cssLoadingStatus.innerHTML =
                '<div class="alert alert-danger">CSS file is not loaded</div>';
        }
    }

    function forceCSSReload() {
        // Instead of dynamically loading CSS, we'll instruct the user to refresh the page
        document.getElementById('css-loading-status').innerHTML =
            '<div class="alert alert-info">To reload CSS, please refresh the page (⌘R) or clear Safari\'s cache (⌥⌘E) and then refresh.</div>';

        // Check the current styles
        checkAppliedStyles();

        // Show additional instructions
        const cssInfo = document.getElementById('css-info');
        cssInfo.innerHTML = `
            <div class="alert alert-warning">
                <h5>Manual CSS Reload Instructions:</h5>
                <ol>
                    <li>Clear Safari's cache: Safari > Settings > Advanced > Show Develop menu, then Develop > Empty Caches (⌥⌘E)</li>
                    <li>Refresh the page (⌘R)</li>
                    <li>If issues persist, try opening the CSS file directly:
                        <a href="${document.getElementById('item-code-lookup-css').getAttribute('href')}" target="_blank">Open CSS file</a>
                    </li>
                </ol>
            </div>
        `;
    }

    function checkAppliedStyles() {
        const testElement = document.getElementById('test-element');
        const styleInfo = document.getElementById('style-info');

        if (!testElement) {
            styleInfo.innerHTML = '<div class="alert alert-danger">Test element not found</div>';
            return;
        }

        const styles = window.getComputedStyle(testElement);
        let styleText = '<ul>';

        // Check background color
        const bgColor = styles.backgroundColor;
        const hasBgColor = bgColor === 'rgb(255, 255, 255)' || bgColor === '#ffffff';
        styleText += `<li>background-color: ${bgColor} ${hasBgColor ? '<span class="test-success">✓</span>' : '<span class="test-failure">✗</span>'}</li>`;

        // Check border
        const border = styles.border;
        const hasBorder = border.includes('1px') && border.includes('solid');
        styleText += `<li>border: ${border} ${hasBorder ? '<span class="test-success">✓</span>' : '<span class="test-failure">✗</span>'}</li>`;

        // Check border radius
        const borderRadius = styles.borderRadius;
        const hasBorderRadius = borderRadius !== '0px';
        styleText += `<li>border-radius: ${borderRadius} ${hasBorderRadius ? '<span class="test-success">✓</span>' : '<span class="test-failure">✗</span>'}</li>`;

        // Check box shadow
        const boxShadow = styles.boxShadow;
        const hasBoxShadow = boxShadow !== 'none';
        styleText += `<li>box-shadow: ${boxShadow} ${hasBoxShadow ? '<span class="test-success">✓</span>' : '<span class="test-failure">✗</span>'}</li>`;

        // Check z-index
        const zIndex = styles.zIndex;
        const hasZIndex = zIndex !== 'auto' && parseInt(zIndex) > 1000;
        styleText += `<li>z-index: ${zIndex} ${hasZIndex ? '<span class="test-success">✓</span>' : '<span class="test-failure">✗</span>'}</li>`;

        styleText += '</ul>';

        // Overall assessment
        const overallSuccess = hasBgColor && hasBorder && hasBorderRadius && hasBoxShadow && hasZIndex;

        if (overallSuccess) {
            styleText += '<div class="alert alert-success">All expected styles are applied correctly!</div>';
        } else {
            styleText += '<div class="alert alert-warning">Some expected styles are missing or incorrect.</div>';
        }

        styleInfo.innerHTML = styleText;
    }
</script>
{% endblock %}
