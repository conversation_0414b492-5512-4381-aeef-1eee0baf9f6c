{% extends 'base.html' %}
{% load static %}

{% block title %}Move {{ item.item_name }} - Inventory Management{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
<!-- Include inline CSS first to ensure styles are applied immediately -->
{% include 'inventory/inline_css.html' %}

<!-- Primary CSS link with cache-busting parameter -->
<link rel="stylesheet" type="text/css" href="{% static 'css/item_code_lookup.css' %}?v={% now 'U' %}" id="item-code-lookup-css">

<!-- Debug information for CSS loading -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Checking for CSS link element...');
    const cssLink = document.getElementById('item-code-lookup-css');
    if (cssLink) {
        console.log('CSS link found:', cssLink.outerHTML);
    } else {
        console.error('CSS link not found in the DOM');
        console.log('All link elements:', document.querySelectorAll('link'));
    }
});
</script>
<style>
    /* Custom styles for the move item page */
    .move-item-container {
        margin-top: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Move Item</h1>
    <a href="{% url 'inventory:item_by_code' item_code=item.item_code %}" class="btn btn-outline-secondary">
        Back to Item
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">Move Item to New Location</h5>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h6>Item Being Moved:</h6>
                    <div class="d-flex align-items-center border p-2 rounded">
                        <div class="me-3">
                            <strong>Code:</strong> <code>{{ item.item_code }}</code>
                        </div>
                        <div class="me-3">
                            <strong>Name:</strong> {{ item.item_name }}
                        </div>
                        <div>
                            <strong>Type:</strong> {{ item.item_type.value }}
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <h6>Current Location:</h6>
                    <p>
                        {% if item.located_in %}
                            <a href="{% url 'inventory:item_detail' pk=item.located_in.pk %}">
                                {{ item.located_in.item_name }} ({{ item.located_in.item_code }})
                            </a>
                        {% else %}
                            <span class="text-muted">Top Level (Not in any container)</span>
                        {% endif %}
                    </p>
                </div>

                <form method="post">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="{{ form.target_location.id_for_label }}" class="form-label">
                            <h6>Target Location:</h6>
                        </label>
                        {{ form.target_location }}
                        {% if form.target_location.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.target_location.errors }}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            Select the container or location where this item should be moved.
                            Select "Top Level" to remove it from any container.
                        </div>
                    </div>

                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary">Move Item</button>
                        <a href="{% url 'inventory:item_detail' pk=item.pk %}" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">About Moving Items</h5>
            </div>
            <div class="card-body">
                <p>
                    Moving an item changes its location in the inventory hierarchy.
                </p>
                <p>
                    When you move an item to a new container, it becomes part of that container's contents.
                    If you move it to "Top Level", it won't be inside any container.
                </p>
                <div class="alert alert-info">
                    <strong>Note:</strong> Any item can contain other items in this system.
                </div>
                <div class="alert alert-warning">
                    <strong>Warning:</strong> Moving a container item does not automatically move its contents.
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Verify jQuery and jQuery UI are loaded
        if (typeof jQuery === 'undefined') {
            console.error('jQuery is not loaded');
            return;
        }

        if (typeof jQuery.ui === 'undefined') {
            console.error('jQuery UI is not loaded');
            return;
        }

        // Initialize the item code lookup widget
        const lookupInputs = document.querySelectorAll('.item-code-lookup');

        lookupInputs.forEach(function(input) {
            const inputId = input.id;
            const hiddenInput = document.getElementById(`${inputId}_hidden`);
            const displayField = document.getElementById(`${inputId}_display`);
            const clearButton = document.getElementById(`${inputId}_clear`);

            // Initialize the widget
            if (hiddenInput && displayField && clearButton) {
                initializeWidget(input, hiddenInput, displayField, clearButton);

                // If there's an initial value, fetch the item details
                if (hiddenInput.value) {
                    fetchItemDetails(hiddenInput.value, displayField, clearButton, input);
                }
            }
        });

        /**
         * Initialize the widget with autocomplete and event handlers
         */
        function initializeWidget(input, hiddenInput, displayField, clearButton) {
            // Set up autocomplete
            $(input).autocomplete({
                source: function(request, response) {
                    // Get the current item ID if we're editing an existing item
                    const currentItemId = document.querySelector('input[name="current_item_id"]')?.value || '';

                    // Make AJAX request to the autocomplete endpoint
                    $.ajax({
                        url: '/inventory/api/items/autocomplete/',
                        dataType: 'json',
                        data: {
                            term: request.term,
                            current_item_id: currentItemId
                        },
                        success: function(data) {
                            // Transform the data for autocomplete
                            const items = data.results.map(function(item) {
                                return {
                                    label: item.code ? `${item.code} - ${item.text}` : item.text,
                                    value: item.code ? `${item.code} - ${item.text}` : item.text,
                                    id: item.id,
                                    code: item.code,
                                    text: item.text
                                };
                            });
                            response(items);
                        }
                    });
                },
                minLength: 2,
                select: function(event, ui) {
                    // Set the hidden input value to the selected item ID
                    hiddenInput.value = ui.item.id;

                    // Show the display field with the selected item
                    if (ui.item.code) {
                        displayField.value = `${ui.item.code} - ${ui.item.text}`;
                    } else {
                        displayField.value = ui.item.text;
                    }

                    // Show the display field and clear button
                    displayField.style.display = 'block';
                    clearButton.style.display = 'block';

                    // Hide the search input
                    input.style.display = 'none';

                    // Prevent the default behavior
                    return false;
                }
            }).autocomplete('instance')._renderItem = function(ul, item) {
                // Enhanced custom rendering for autocomplete items
                let html = '<div class="autocomplete-item">';

                if (item.code) {
                    html += `<div><code>${item.code}</code> - <strong>${item.text}</strong></div>`;
                    if (item.type) {
                        html += `<div class="item-type text-muted small">${item.type}</div>`;
                    }
                } else {
                    html += `<div><strong>${item.text}</strong></div>`;
                    if (item.type) {
                        html += `<div class="item-type text-muted small">${item.type}</div>`;
                    }
                }

                html += '</div>';

                return $('<li>')
                    .append(html)
                    .appendTo(ul);
            };

            // Handle clear button click
            clearButton.addEventListener('click', function() {
                // Clear the hidden input
                hiddenInput.value = '';

                // Clear and hide the display field
                displayField.value = '';
                displayField.style.display = 'none';

                // Hide the clear button
                clearButton.style.display = 'none';

                // Show and clear the search input
                input.style.display = 'block';
                input.value = '';
                input.focus();
            });
        }

        /**
         * Fetch item details for an initial value
         */
        function fetchItemDetails(itemId, displayField, clearButton, input) {
            if (!itemId) return;

            // Make AJAX request to get item details
            $.ajax({
                url: '/inventory/api/items/autocomplete/',
                dataType: 'json',
                data: {
                    term: itemId,  // Use the ID as the search term
                    exact_match: true
                },
                success: function(data) {
                    if (data.results && data.results.length > 0) {
                        const item = data.results[0];

                        // Set the display field
                        if (item.code) {
                            displayField.value = `${item.code} - ${item.text}`;
                        } else {
                            displayField.value = item.text;
                        }

                        // Show the display field and clear button
                        displayField.style.display = 'block';
                        clearButton.style.display = 'block';

                        // Hide the search input
                        if (input) {
                            input.style.display = 'none';
                        }
                    }
                }
            });
        }
    });
</script>
{% endblock %}
{% endblock %}
