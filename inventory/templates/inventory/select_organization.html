{% extends 'base_minimal.html' %}
{% load inventory_filters %}

{% block title %}Select Organization - Inventory Management{% endblock %}

{% block extra_head %}
<style>
    .org-card {
        transition: transform 0.2s, box-shadow 0.2s;
    }
    .org-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .selected-org {
        border: 2px solid #007bff;
        background-color: rgba(0, 123, 255, 0.05);
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Select Organization</h4>
                    {% if user.is_superuser %}
                    <a href="/admin/" class="btn btn-light btn-sm">
                        <i class="bi bi-gear-fill"></i> Admin Interface
                    </a>
                    {% endif %}
                </div>
                <div class="card-body">
                    <p class="lead mb-4">Please select an organization to continue:</p>
                    
                    {% if organizations %}
                    <div class="row">
                        {% for org in organizations %}
                            <div class="col-md-6 mb-4">
                                <div class="card h-100 org-card {% if org.id|stringformat:'i' == request.session.org_id %}selected-org{% endif %}">
                                    <div class="card-body d-flex flex-column">
                                        {% if org.logo %}
                                            <div class="text-center mb-3">
                                                <img src="{{ org.logo.url }}" alt="{{ org.name }}" class="img-fluid" style="max-height: 80px;">
                                            </div>
                                        {% endif %}
                                        <h5 class="card-title">{{ org.name }}</h5>
                                        <p class="card-text text-muted">Code: {{ org.code }}</p>
                                        <div class="mt-auto">
                                            <form method="post" action="{% url 'inventory:switch_organization' org_id=org.id %}" class="org-select-form">
                                                {% csrf_token %}
                                                <input type="hidden" name="next" value="/inventory/items/">
                                                <button type="submit" class="btn btn-primary w-100">
                                                    {% if org.id|stringformat:'i' == request.session.org_id %}
                                                        Currently Selected
                                                    {% else %}
                                                        Select
                                                    {% endif %}
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="alert alert-warning">
                        <h5 class="alert-heading">No Organizations Available</h5>
                        <p>There are no organizations available for your account.</p>
                        {% if user.is_superuser %}
                        <hr>
                        <p class="mb-0">As an administrator, you can create organizations in the admin interface.</p>
                        <a href="/admin/inventory/organization/add/" class="btn btn-primary mt-3">
                            Create Organization
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                    
                    <div class="mt-4 text-center">
                        {% if user.is_superuser %}
                        <a href="/admin/inventory/organization/add/" class="btn btn-success">
                            <i class="bi bi-plus-circle"></i> Create New Organization
                        </a>
                        {% endif %}
                        <form method="post" action="{% url 'inventory:logout' %}" class="d-inline">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-outline-secondary ms-2">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add debug logging for organization selection form submission
    const orgForms = document.querySelectorAll('.org-select-form');
    orgForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            console.log('Organization form submission initiated');
            console.log('Form action:', this.action);
            console.log('Form method:', this.method);

            // Get the CSRF token
            const csrfToken = this.querySelector('input[name="csrfmiddlewaretoken"]').value;
            console.log('CSRF token present:', !!csrfToken);

            // Get the next URL
            const nextUrl = this.querySelector('input[name="next"]').value;
            console.log('Next URL:', nextUrl);

            // Store organization ID in localStorage as backup
            const orgId = this.action.split('/').slice(-2)[0];
            localStorage.setItem('backup_org_id', orgId);
            console.log(`Stored backup_org_id=${orgId} in localStorage`);

            // Allow the form to submit normally
        });
    });

    // Add debug logging for logout form submission
    const logoutForm = document.querySelector('form[action*="logout"]');
    if (logoutForm) {
        logoutForm.addEventListener('submit', function(e) {
            console.log('Logout form submission initiated');
            console.log('Form action:', this.action);
            console.log('Form method:', this.method);

            // Get the CSRF token
            const csrfToken = this.querySelector('input[name="csrfmiddlewaretoken"]').value;
            console.log('CSRF token present:', !!csrfToken);

            // Allow the form to submit normally
        });
    }
});
</script>
{% endblock %}
{% endblock %}
