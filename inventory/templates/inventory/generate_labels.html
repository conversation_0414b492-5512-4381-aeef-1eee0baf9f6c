{% extends 'base.html' %}

{% block title %}Generate QR Code Labels - Inventory Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Generate QR Code Labels</h1>
</div>

<div class="row">
    <div class="col-lg-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">Option 1: Small Round QR Labels</h5>
            </div>
            <div class="card-body">
                <p>
                    Generate a PDF sheet of small round QR codes that link to <code>http://sopo.me/{code}</code>.
                    These are designed to fit on a sheet with 154 labels (0.5" x 0.5" each).
                </p>
                
                <form method="post">
                    {% csrf_token %}
                    <input type="hidden" name="generate_pdf" value="1">
                    <div class="alert alert-info">
                        This will generate a PDF with 154 unique QR codes (or as many as possible),
                        ready to print on a sheet of 0.5" x 0.5" round labels.
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-file-earmark-pdf"></i> Download Round QR Labels
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">Option 2: Rectangular QR Labels</h5>
            </div>
            <div class="card-body">
                <p>
                    Generate a PDF sheet of rectangular QR codes with text that link to <code>http://sopo.me/{code}</code>.
                    These are designed to fit on a sheet with 80 labels (0.5" x 1.75" each).
                </p>
                
                <form method="post">
                    {% csrf_token %}
                    <input type="hidden" name="generate_rect_pdf" value="1">
                    <div class="alert alert-info">
                        This will generate a PDF with 80 unique QR codes (or as many as possible),
                        ready to print on a sheet of 0.5" x 1.75" rectangular labels.
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-file-earmark-pdf"></i> Download Rectangular QR Labels
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Option 2: Generate CSV for 3rd Party Printing</h5>
            </div>
            <div class="card-body">
                <p>
                    Generate a CSV file containing unique codes and their corresponding URLs.
                    This is useful for third-party label printing services.
                </p>
                
                <form method="post">
                    {% csrf_token %}
                    <input type="hidden" name="generate_csv" value="1">
                    <div class="mb-3">
                        <label for="{{ form.quantity.id_for_label }}" class="form-label">
                            Number of Unique Codes
                        </label>
                        {{ form.quantity }}
                        {% if form.quantity.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.quantity.errors }}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            Specify how many unique codes you need for your labels.
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="bi bi-file-earmark-text"></i> Download CSV File
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">About QR Code Labels</h5>
            </div>
            <div class="card-body">
                <h6>How to use generated QR codes:</h6>
                <ol>
                    <li>Generate QR codes using one of the options on this page.</li>
                    <li>For PDF option: Print the sheet on label paper (0.5" x 0.5" labels).</li>
                    <li>For CSV option: Upload the file to a third-party label printing service.</li>
                    <li>Attach the printed labels to physical items.</li>
                    <li>When ready to add an item to the system, scan the QR code or use the
                        <a href="{% url 'inventory:item_lookup' %}">Item Lookup</a> feature.
                    </li>
                    <li>Enter the item details when prompted.</li>
                </ol>
                
                <div class="alert alert-info">
                    <strong>Note:</strong> Generated codes are guaranteed to be unique at the time of generation.
                    If you don't use all labels immediately, it's recommended to verify they're still available
                    when you use them later.
                </div>
                
                <div class="alert alert-warning">
                    <strong>Tip:</strong> The PDF option is best for immediate printing, while the CSV option
                    gives you more flexibility with third-party label printing services.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
