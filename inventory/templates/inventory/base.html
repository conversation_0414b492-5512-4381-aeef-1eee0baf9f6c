<div class="dropdown ms-3">
    <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="orgDropdown" data-bs-toggle="dropdown" aria-expanded="false">
        {% if request.session.org_id %}
            {% with org=organizations|filter_by_id:request.session.org_id %}
                {{ org.name }}
            {% endwith %}
        {% else %}
            Select Organization
        {% endif %}
    </button>
    <ul class="dropdown-menu" aria-labelledby="orgDropdown">
        {% for org in organizations %}
            <li>
                <a class="dropdown-item {% if org.id|stringformat:'i' == request.session.org_id %}active{% endif %}" 
                   href="?org_id={{ org.id }}">
                    {{ org.name }}
                </a>
            </li>
        {% endfor %}
    </ul>
</div>