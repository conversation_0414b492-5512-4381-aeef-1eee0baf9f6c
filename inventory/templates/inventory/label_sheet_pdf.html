<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Print Labels</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: white;
        }
        
        .labels-container {
            display: flex;
            flex-wrap: wrap;
            margin: 0;
            padding: 0.5in;
        }
        
        .label-item {
            width: 33.333%;
            padding: 10px;
            box-sizing: border-box;
        }
        
        .label-content {
            border: 1px dashed #999;
            padding: 15px;
            text-align: center;
            height: 100%;
        }
        
        .code-line {
            margin-bottom: 10px;
        }
        
        .code {
            font-family: monospace;
            font-size: 16px;
            font-weight: bold;
            letter-spacing: 1px;
        }
        
        .code-images {
            display: flex;
            justify-content: space-around;
        }
        
        .barcode-container {
            width: 45%;
        }
        
        .qrcode-container {
            width: 45%;
        }
        
        .barcode-image,
        .qrcode-image {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <div class="labels-container">
        {% for label in labels %}
        <div class="label-item">
            <div class="label-content">
                <div class="code-line">
                    <span class="code">{{ label.code }}</span>
                </div>
                <div class="code-images">
                    <div class="barcode-container">
                        <img src="data:image/png;base64,{{ label.barcode }}" alt="Barcode for {{ label.code }}" class="barcode-image">
                    </div>
                    <div class="qrcode-container">
                        <img src="data:image/png;base64,{{ label.qrcode }}" alt="QR Code for {{ label.code }}" class="qrcode-image">
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</body>
</html>