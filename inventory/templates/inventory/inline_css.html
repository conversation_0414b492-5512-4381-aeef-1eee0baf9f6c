<style>
/**
 * Item Code Lookup Widget Styles - Inline Version
 */

.item-code-lookup-container {
    position: relative;
    margin-bottom: 1rem;
    /* Add a subtle highlight to the container when focused */
    transition: all 0.2s ease-in-out;
}

/* Add focus styling to the container when the input is focused */
.item-code-lookup:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    border-color: #80bdff;
}

/* Enhanced autocomplete dropdown styling */
.ui-autocomplete {
    max-height: 300px;
    overflow-y: auto;
    overflow-x: hidden;
    z-index: 1050 !important;
    background-color: #ffffff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    padding: 0.5rem 0;
    margin-top: 2px;
    width: auto !important;
    min-width: 100%;
}

.ui-autocomplete .ui-menu-item {
    padding: 0;
    margin: 0;
    border-bottom: 1px solid #f0f0f0;
}

.ui-autocomplete .ui-menu-item:last-child {
    border-bottom: none;
}

.ui-autocomplete .ui-menu-item-wrapper {
    padding: 0.75rem 1rem;
    transition: background-color 0.15s ease-in-out;
}

.ui-autocomplete .ui-state-active {
    margin: 0;
    background-color: #e9ecef;
    border-color: #dee2e6;
    color: #212529;
    font-weight: 500;
}

.ui-autocomplete .ui-menu-item-wrapper:hover {
    background-color: #f8f9fa;
}

.autocomplete-item {
    padding: 0.25rem 0;
}

.autocomplete-item code {
    background-color: #f0f0f0;
    padding: 0.2rem 0.4rem;
    border-radius: 0.2rem;
    font-size: 0.9em;
    border: 1px solid #e0e0e0;
}

.autocomplete-item .item-type {
    margin-top: 0.25rem;
    color: #6c757d;
    font-size: 0.85em;
}

.selected-item-display {
    margin-top: 0.25rem;
}

.item-display {
    padding: 0.5rem;
    margin-bottom: 0;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    width: 100%;
}

.clear-item-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    background-color: #fff;
    border-color: #ced4da;
    color: #6c757d;
    transition: all 0.15s ease-in-out;
}

.clear-item-btn:hover {
    background-color: #f8f9fa;
    color: #dc3545;
}

.clear-item-btn i {
    font-size: 0.75rem;
}
</style>
