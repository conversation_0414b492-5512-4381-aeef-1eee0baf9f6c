{% extends 'base.html' %}
{% load static %}

{% block title %}Test Location Picker - Inventory Management{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
<!-- Include inline CSS first to ensure styles are applied immediately -->
{% include 'inventory/inline_css.html' %}

<!-- Primary CSS link with cache-busting parameter -->
<link rel="stylesheet" type="text/css" href="{% static 'css/item_code_lookup.css' %}?v={% now 'U' %}" id="item-code-lookup-css">

<!-- Debug information for CSS loading -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Checking for CSS link element...');
    const cssLink = document.getElementById('item-code-lookup-css');
    if (cssLink) {
        console.log('CSS link found:', cssLink.outerHTML);
    } else {
        console.error('CSS link not found in the DOM');
        console.log('All link elements:', document.querySelectorAll('link'));
    }
});
</script>
<style>
    /* Additional test styles */
    .test-container {
        margin: 2rem 0;
        padding: 1rem;
        border: 1px solid #ddd;
        border-radius: 0.25rem;
    }

    .debug-info {
        margin-top: 1rem;
        padding: 1rem;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Test Location Picker</h1>
    <a href="{% url 'inventory:item_list' %}" class="btn btn-outline-secondary">
        Back to Items
    </a>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title">Location Picker Test</h5>
    </div>
    <div class="card-body">
        <p class="mb-4">This page is for testing the location picker component styling.</p>

        <form method="post" id="test-form">
            {% csrf_token %}
            <div class="mb-3">
                <label for="location-picker" class="form-label">Select a Location:</label>
                <div class="item-code-lookup-container">
                    <div class="input-group">
                        <input type="text" name="location_search" id="location-picker"
                               class="form-control item-code-lookup"
                               placeholder="Enter item code or name" autocomplete="off">
                        <input type="hidden" name="location" id="location-picker_hidden" value="">
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary clear-item-btn" type="button" id="location-picker_clear"
                                    style="display: none;">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="selected-item-display mt-1">
                        <input type="text" id="location-picker_display" class="form-control-plaintext item-display"
                               readonly style="display: none;">
                    </div>
                </div>
                <div class="form-text">
                    Start typing to search for a location by code or name.
                </div>
            </div>

            <div class="mt-4">
                <button type="button" class="btn btn-primary" id="test-button">Test Selection</button>
            </div>
        </form>

        <div class="debug-info">
            <h6>Debug Information:</h6>
            <div id="debug-output">
                <p>No selection yet.</p>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title">CSS Troubleshooting</h5>
    </div>
    <div class="card-body">
        <h6>CSS Loading Status:</h6>
        <div id="css-status">Checking...</div>

        <h6 class="mt-3">Applied Styles:</h6>
        <div id="style-info">Loading...</div>

        <h6 class="mt-3">Browser Cache:</h6>
        <p>If you're not seeing style changes, try clearing your browser cache or doing a hard refresh (Ctrl+F5 or Cmd+Shift+R).</p>

        <button class="btn btn-outline-secondary mt-2" id="force-refresh">
            Force CSS Reload
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Verify jQuery and jQuery UI are loaded
        if (typeof jQuery === 'undefined') {
            console.error('jQuery is not loaded');
            document.getElementById('css-status').innerHTML = '<span class="text-danger">Error: jQuery is not loaded</span>';
            return;
        }

        if (typeof jQuery.ui === 'undefined') {
            console.error('jQuery UI is not loaded');
            document.getElementById('css-status').innerHTML = '<span class="text-danger">Error: jQuery UI is not loaded</span>';
            return;
        }

        // Check if CSS is loaded
        const cssLoaded = checkCSSLoaded('item_code_lookup.css');
        document.getElementById('css-status').innerHTML = cssLoaded ?
            '<span class="text-success">CSS file appears to be loaded</span>' :
            '<span class="text-danger">CSS file may not be loaded correctly</span>';

        // Display applied styles
        displayAppliedStyles();

        // Initialize the location picker
        initializeLocationPicker();

        // Force CSS reload button
        document.getElementById('force-refresh').addEventListener('click', function() {
            const timestamp = new Date().getTime();
            const cssLink = document.createElement('link');
            cssLink.rel = 'stylesheet';
            cssLink.href = `/static/css/item_code_lookup.css?v=${timestamp}`;
            document.head.appendChild(cssLink);

            setTimeout(function() {
                displayAppliedStyles();
                document.getElementById('css-status').innerHTML = '<span class="text-success">CSS reload attempted</span>';
            }, 500);
        });

        // Test button
        document.getElementById('test-button').addEventListener('click', function() {
            const locationId = document.getElementById('location-picker_hidden').value;
            const locationName = document.getElementById('location-picker_display').value;

            const debugOutput = document.getElementById('debug-output');
            if (locationId) {
                debugOutput.innerHTML = `<p>Selected location ID: <strong>${locationId}</strong></p>
                                        <p>Display value: <strong>${locationName}</strong></p>`;
            } else {
                debugOutput.innerHTML = '<p>No location selected.</p>';
            }
        });
    });

    function checkCSSLoaded(filename) {
        let loaded = false;
        for (let i = 0; i < document.styleSheets.length; i++) {
            const sheet = document.styleSheets[i];
            try {
                if (sheet.href && sheet.href.includes(filename)) {
                    loaded = true;
                    break;
                }
            } catch (e) {
                // Ignore security errors for cross-origin stylesheets
            }
        }
        return loaded;
    }

    function displayAppliedStyles() {
        const styleInfo = document.getElementById('style-info');
        const testElement = document.querySelector('.ui-autocomplete');

        if (!testElement) {
            // Create a temporary element to check styles
            const temp = document.createElement('div');
            temp.className = 'ui-autocomplete';
            temp.style.position = 'absolute';
            temp.style.left = '-9999px';
            document.body.appendChild(temp);

            const styles = window.getComputedStyle(temp);
            let styleText = '<ul>';
            styleText += `<li>background-color: ${styles.backgroundColor}</li>`;
            styleText += `<li>border: ${styles.border}</li>`;
            styleText += `<li>border-radius: ${styles.borderRadius}</li>`;
            styleText += `<li>box-shadow: ${styles.boxShadow}</li>`;
            styleText += `<li>z-index: ${styles.zIndex}</li>`;
            styleText += '</ul>';

            styleInfo.innerHTML = styleText;

            document.body.removeChild(temp);
        } else {
            const styles = window.getComputedStyle(testElement);
            let styleText = '<ul>';
            styleText += `<li>background-color: ${styles.backgroundColor}</li>`;
            styleText += `<li>border: ${styles.border}</li>`;
            styleText += `<li>border-radius: ${styles.borderRadius}</li>`;
            styleText += `<li>box-shadow: ${styles.boxShadow}</li>`;
            styleText += `<li>z-index: ${styles.zIndex}</li>`;
            styleText += '</ul>';

            styleInfo.innerHTML = styleText;
        }
    }

    function initializeLocationPicker() {
        const input = document.getElementById('location-picker');
        const hiddenInput = document.getElementById('location-picker_hidden');
        const displayField = document.getElementById('location-picker_display');
        const clearButton = document.getElementById('location-picker_clear');

        // Set up autocomplete
        $(input).autocomplete({
            source: function(request, response) {
                // Make AJAX request to the autocomplete endpoint
                $.ajax({
                    url: '/inventory/api/items/autocomplete/',
                    dataType: 'json',
                    data: {
                        term: request.term
                    },
                    success: function(data) {
                        // Transform the data for autocomplete
                        const items = data.results.map(function(item) {
                            return {
                                label: item.code ? `${item.code} - ${item.text}` : item.text,
                                value: item.code ? `${item.code} - ${item.text}` : item.text,
                                id: item.id,
                                code: item.code,
                                text: item.text,
                                type: item.type
                            };
                        });
                        response(items);
                    }
                });
            },
            minLength: 2,
            select: function(event, ui) {
                // Set the hidden input value to the selected item ID
                hiddenInput.value = ui.item.id;

                // Show the display field with the selected item
                if (ui.item.code) {
                    displayField.value = `${ui.item.code} - ${ui.item.text}`;
                } else {
                    displayField.value = ui.item.text;
                }

                // Show the display field and clear button
                displayField.style.display = 'block';
                clearButton.style.display = 'block';

                // Hide the search input
                input.style.display = 'none';

                // Prevent the default behavior
                return false;
            }
        }).autocomplete('instance')._renderItem = function(ul, item) {
            // Enhanced custom rendering for autocomplete items
            let html = '<div class="autocomplete-item">';

            if (item.code) {
                html += `<div><code>${item.code}</code> - <strong>${item.text}</strong></div>`;
                if (item.type) {
                    html += `<div class="item-type text-muted small">${item.type}</div>`;
                }
            } else {
                html += `<div><strong>${item.text}</strong></div>`;
                if (item.type) {
                    html += `<div class="item-type text-muted small">${item.type}</div>`;
                }
            }

            html += '</div>';

            return $('<li>')
                .append(html)
                .appendTo(ul);
        };

        // Handle clear button click
        clearButton.addEventListener('click', function() {
            // Clear the hidden input
            hiddenInput.value = '';

            // Clear and hide the display field
            displayField.value = '';
            displayField.style.display = 'none';

            // Hide the clear button
            clearButton.style.display = 'none';

            // Show and clear the search input
            input.style.display = 'block';
            input.value = '';
            input.focus();
        });
    }
</script>
{% endblock %}
