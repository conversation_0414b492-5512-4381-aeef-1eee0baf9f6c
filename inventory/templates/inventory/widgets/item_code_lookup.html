{% load i18n %}
<div class="item-code-lookup-container">
  <div class="input-group">
    <input type="{{ widget.type }}" name="{{ widget.name }}_search" id="{{ widget.attrs.id }}" 
           {% if widget.value != None %}value="{{ widget.value|stringformat:'s' }}"{% endif %}
           {% include "django/forms/widgets/attrs.html" %}>
    <input type="hidden" name="{{ widget.name }}" id="{{ widget.hidden_input_attrs.id }}" 
           value="{{ widget.hidden_input_attrs.value }}">
    <div class="input-group-append">
      <button class="btn btn-outline-secondary clear-item-btn" type="button" id="{{ widget.clear_attrs.id }}" 
              style="{{ widget.clear_attrs.style }}">
        <i class="fas fa-times"></i>
      </button>
    </div>
  </div>
  <div class="selected-item-display mt-1">
    <input type="text" id="{{ widget.display_attrs.id }}" class="{{ widget.display_attrs.class }}" 
           readonly style="{{ widget.display_attrs.style }}">
  </div>
</div>
