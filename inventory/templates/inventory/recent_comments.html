{% extends 'base.html' %}
{% load inventory_filters %}

{% block title %}Recent Comments - Inventory Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-comments me-2"></i>Recent Comments</h1>
    <a href="{% url 'inventory:item_list' %}" class="btn btn-outline-primary">
        <i class="fas fa-arrow-left me-1"></i>Back to Items
    </a>
</div>

{% if comments %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            Recent Comments
            <span class="badge bg-secondary">{{ page_obj.paginator.count }} total</span>
        </h5>

        <!-- Archive Toggle -->
        {% if user.is_authenticated %}
        <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="showArchivedToggle"
                   {% if show_archived %}checked{% endif %}
                   onchange="toggleArchivedComments(this.checked)">
            <label class="form-check-label" for="showArchivedToggle">
                Show Archived
            </label>
        </div>
        {% endif %}
    </div>
    <div class="card-body">
        {% for comment in comments %}
        <div class="comment-item border-bottom pb-4 mb-4 {% if comment.is_archived %}archived-comment{% endif %}">
            <!-- Comment Header -->
            <div class="d-flex justify-content-between align-items-start mb-3">
                <div class="d-flex align-items-center">
                    {% if comment.is_guest_comment %}
                        <i class="fas fa-user-circle text-muted me-2" title="Guest User"></i>
                        <strong>{{ comment.commenter_name }}</strong>
                        <span class="badge bg-light text-dark ms-2">Guest</span>
                    {% else %}
                        <i class="fas fa-user-check text-primary me-2" title="Authenticated User"></i>
                        <strong>{{ comment.commenter_name }}</strong>
                        <span class="badge bg-primary ms-2">User</span>
                    {% endif %}

                    {% if comment.reason %}
                        <span class="badge bg-warning text-dark ms-2">
                            <i class="fas fa-tag me-1"></i>{{ comment.reason.value }}
                        </span>
                    {% endif %}

                    <!-- Archive status badge -->
                    {% if comment.is_archived %}
                        <span class="badge bg-secondary ms-2" title="{{ comment.archive_status_display }}">
                            <i class="fas fa-archive me-1"></i>Archived
                        </span>
                    {% endif %}
                </div>

                <div class="text-muted small d-flex align-items-center">
                    <span class="me-2">{{ comment.created_at|date:"M j, Y g:i A" }}</span>
                    {% if comment.is_edited %}
                        <span class="text-muted me-2">(edited)</span>
                    {% endif %}

                    <!-- Action buttons for authenticated users -->
                    {% if user.is_authenticated %}
                        <div class="btn-group btn-group-sm" role="group">
                            <!-- Edit button (only for comment owner) -->
                            {% if comment.user == user %}
                                <a href="{% url 'inventory:edit_comment' comment_id=comment.id %}"
                                   class="btn btn-outline-secondary" title="Edit Comment">
                                    <i class="fas fa-edit"></i>
                                </a>
                            {% endif %}

                            <!-- Archive/Unarchive button -->
                            {% if comment.is_archived %}
                                <form method="post" action="{% url 'inventory:unarchive_comment' comment_id=comment.id %}" class="d-inline">
                                    {% csrf_token %}
                                    <input type="hidden" name="next" value="{{ request.get_full_path }}">
                                    <button type="submit" class="btn btn-outline-success" title="Unarchive Comment">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                </form>
                            {% else %}
                                <form method="post" action="{% url 'inventory:archive_comment' comment_id=comment.id %}" class="d-inline">
                                    {% csrf_token %}
                                    <input type="hidden" name="next" value="{{ request.get_full_path }}">
                                    <button type="submit" class="btn btn-outline-warning" title="Archive Comment">
                                        <i class="fas fa-archive"></i>
                                    </button>
                                </form>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Item Link -->
            <div class="mb-2">
                <small class="text-muted">Comment on:</small>
                <a href="{% url 'inventory:item_by_code' item_code=comment.item.item_code %}" 
                   class="text-decoration-none">
                    <i class="fas fa-box me-1"></i>
                    <strong>{{ comment.item.item_name }}</strong>
                    <code class="ms-1">{{ comment.item.item_code }}</code>
                </a>
            </div>
            
            <!-- Comment Content -->
            <div class="comment-text">
                {{ comment.comment_text|linebreaks }}
            </div>
            
            <!-- Photo if present -->
            {% if comment.photo %}
            <div class="comment-photo mt-3">
                <a href="{{ comment.photo.url }}" target="_blank">
                    <img src="{{ comment.photo.url }}" alt="Comment photo" 
                         class="img-thumbnail" style="max-width: 300px; max-height: 200px;">
                </a>
            </div>
            {% endif %}
        </div>
        {% endfor %}
    </div>
</div>

<!-- Pagination -->
{% if page_obj.has_other_pages %}
<nav aria-label="Comments pagination" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page=1">First</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
            </li>
        {% endif %}
        
        <li class="page-item active">
            <span class="page-link">
                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
            </span>
        </li>
        
        {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last</a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<div class="card">
    <div class="card-body text-center py-5">
        <i class="fas fa-comments fa-4x text-muted mb-4"></i>
        <h4 class="text-muted">No Comments Yet</h4>
        <p class="text-muted">Comments will appear here as they are added to items.</p>
        <a href="{% url 'inventory:item_list' %}" class="btn btn-primary">
            <i class="fas fa-box me-1"></i>Browse Items
        </a>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .archived-comment {
        opacity: 0.7;
        background-color: #f8f9fa;
        border-left: 3px solid #6c757d;
        padding-left: 10px;
    }

    .comment-item {
        transition: opacity 0.3s ease;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
function toggleArchivedComments(showArchived) {
    const currentUrl = new URL(window.location);
    if (showArchived) {
        currentUrl.searchParams.set('show_archived', 'true');
    } else {
        currentUrl.searchParams.delete('show_archived');
    }
    window.location.href = currentUrl.toString();
}
</script>
{% endblock %}
