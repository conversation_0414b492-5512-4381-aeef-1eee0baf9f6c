{% extends 'base.html' %}

{% block title %}Manage Users - {{ organization.name }}{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Manage Users - {{ organization.name }}</h1>
        <a href="{% url 'inventory:add_organization_user' %}" class="btn btn-primary">
            <i class="bi bi-person-plus"></i> Add User
        </a>
    </div>
    
    <div class="card shadow-sm">
        <div class="card-body">
            {% if org_users %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Admin</th>
                                <th>Can Edit</th>
                                <th>Can Add</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for org_user in org_users %}
                                <tr>
                                    <td>{{ org_user.user.username }}</td>
                                    <td>{{ org_user.user.email }}</td>
                                    <td>
                                        {% if org_user.is_admin %}
                                            <span class="badge bg-success">Yes</span>
                                        {% else %}
                                            <span class="badge bg-secondary">No</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if org_user.can_edit %}
                                            <span class="badge bg-success">Yes</span>
                                        {% else %}
                                            <span class="badge bg-secondary">No</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if org_user.can_add %}
                                            <span class="badge bg-success">Yes</span>
                                        {% else %}
                                            <span class="badge bg-secondary">No</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'inventory:edit_organization_user' user_id=org_user.user.id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-pencil"></i> Edit
                                        </a>
                                        {% if org_user.user != request.user %}
                                            <a href="{% url 'inventory:remove_organization_user' user_id=org_user.user.id %}" class="btn btn-sm btn-outline-danger">
                                                <i class="bi bi-person-x"></i> Remove
                                            </a>
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    No users found in this organization.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}