{% extends 'base.html' %}

{% block title %}Print Labels - Inventory Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Print Labels</h1>
    <div>
        <button onclick="window.print()" class="btn btn-primary">
            <i class="bi bi-printer"></i> Print Labels
        </button>
        <form method="post" class="d-inline">
            {% csrf_token %}
            <input type="hidden" name="download_pdf" value="1">
            {% for label in labels %}
            <input type="hidden" name="generated_codes" value="{{ label.code }}">
            {% endfor %}
            <button type="submit" class="btn btn-outline-primary">
                <i class="bi bi-file-earmark-pdf"></i> Download PDF
            </button>
        </form>
        <a href="{% url 'inventory:generate_labels' %}" class="btn btn-outline-secondary">
            Generate More
        </a>
    </div>
</div>

<div class="alert alert-info mb-4">
    <h5>Ready to Print</h5>
    <p class="mb-0">
        {{ labels|length }} unique labels have been generated. Click the "Print Labels" button above to print this page,
        then cut along the dotted lines to create individual labels.
    </p>
</div>

<div class="labels-container">
    {% for label in labels %}
    <div class="label-item">
        <div class="label-content">
            <div class="code-line">
                <span class="code">{{ label.code }}</span>
            </div>
            <div class="code-images">
                <div class="barcode-container">
                    <img src="data:image/png;base64,{{ label.barcode }}" alt="Barcode for {{ label.code }}" class="barcode-image">
                </div>
                <div class="qrcode-container">
                    <img src="data:image/png;base64,{{ label.qrcode }}" alt="QR Code for {{ label.code }}" class="qrcode-image">
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endblock %}

{% block extra_js %}
<style>
    @media print {
        body {
            margin: 0;
            padding: 0;
            background: white;
        }
        
        .navbar, 
        .alert,
        .btn,
        h1, 
        .d-flex {
            display: none !important;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
            padding: 0;
            margin: 0;
        }
        
        .labels-container {
            page-break-inside: avoid;
        }
    }
    
    .labels-container {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -10px;
    }
    
    .label-item {
        width: 33.333%;
        padding: 10px;
        box-sizing: border-box;
    }
    
    .label-content {
        border: 1px dashed #999;
        padding: 15px;
        text-align: center;
        height: 100%;
    }
    
    .code-line {
        margin-bottom: 10px;
    }
    
    .code {
        font-family: monospace;
        font-size: 16px;
        font-weight: bold;
        letter-spacing: 1px;
    }
    
    .code-images {
        display: flex;
        justify-content: space-around;
    }
    
    .barcode-container {
        width: 45%;
    }
    
    .qrcode-container {
        width: 45%;
    }
    
    .barcode-image,
    .qrcode-image {
        max-width: 100%;
        height: auto;
    }
    
    @media (max-width: 768px) {
        .label-item {
            width: 50%;
        }
    }
    
    @media (max-width: 480px) {
        .label-item {
            width: 100%;
        }
    }
</style>
{% endblock %}
