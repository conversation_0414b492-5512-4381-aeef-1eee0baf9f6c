{% extends 'base.html' %}

{% block title %}Bulk Move Items - Inventory Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Bulk Move Items</h1>
    <a href="{% url 'inventory:item_list' %}" class="btn btn-outline-secondary">
        Back to Item List
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title">Move Multiple Items</h5>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h6>Items Being Moved ({{ items.count }}):</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Code</th>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Current Location</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in items %}
                                <tr>
                                    <td><code>{{ item.item_code }}</code></td>
                                    <td>{{ item.item_name }}</td>
                                    <td>{{ item.item_type.value }}</td>
                                    <td>
                                        {% if item.located_in %}
                                            {{ item.located_in.item_name }} ({{ item.located_in.item_code }})
                                        {% else %}
                                            <span class="text-muted">Top Level</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    {% for id in selected_items.split %}
                        <input type="hidden" name="selected_items" value="{{ id }}">
                    {% endfor %}
                    
                    <div class="mb-3">
                        <label for="{{ form.target_location.id_for_label }}" class="form-label">
                            <h6>Target Location:</h6>
                        </label>
                        {{ form.target_location }}
                        {% if form.target_location.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.target_location.errors }}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            Select the container or location where these items should be moved.
                            Select "Top Level" to remove them from any container.
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary">Move Items</button>
                        <a href="{% url 'inventory:item_list' %}" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">About Bulk Moving</h5>
            </div>
            <div class="card-body">
                <p>
                    Bulk move allows you to change the location of multiple items at once.
                </p>
                <p>
                    All selected items will be moved to the same target location.
                </p>
                <div class="alert alert-warning">
                    <strong>Warning:</strong> If you're moving container items, their contents will not be moved automatically.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
