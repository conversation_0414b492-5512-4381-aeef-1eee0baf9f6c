{% extends 'base.html' %}

{% block title %}Edit Comment - {{ item.item_name }} - Inventory Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-edit me-2"></i>Edit Comment</h1>
    <a href="{% url 'inventory:item_by_code' item_code=item.item_code %}" class="btn btn-outline-primary">
        <i class="fas fa-arrow-left me-1"></i>Back to Item
    </a>
</div>

<!-- Item Context -->
<div class="card mb-4">
    <div class="card-body">
        <div class="d-flex align-items-center">
            <i class="fas fa-box me-2 text-muted"></i>
            <span class="text-muted me-2">Editing comment on:</span>
            <a href="{% url 'inventory:item_by_code' item_code=item.item_code %}" class="text-decoration-none">
                <strong>{{ item.item_name }}</strong>
                <code class="ms-2">{{ item.item_code }}</code>
            </a>
        </div>
    </div>
</div>

<!-- Edit Form -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-comment me-2"></i>Edit Your Comment
        </h5>
    </div>
    <div class="card-body">
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            
            <!-- Comment text -->
            <div class="mb-3">
                <label for="{{ form.comment_text.id_for_label }}" class="form-label">
                    <i class="fas fa-comment me-1"></i>Comment <span class="text-danger">*</span>
                </label>
                {{ form.comment_text }}
                {% if form.comment_text.errors %}
                    <div class="text-danger small">{{ form.comment_text.errors.0 }}</div>
                {% endif %}
            </div>
            
            <!-- Reason dropdown -->
            <div class="mb-3">
                <label for="{{ form.reason.id_for_label }}" class="form-label">
                    <i class="fas fa-tag me-1"></i>Reason (Optional)
                </label>
                {{ form.reason }}
                {% if form.reason.errors %}
                    <div class="text-danger small">{{ form.reason.errors.0 }}</div>
                {% endif %}
            </div>
            
            <!-- Photo upload -->
            <div class="mb-3">
                <label for="{{ form.photo.id_for_label }}" class="form-label">
                    <i class="fas fa-camera me-1"></i>Photo (Optional)
                </label>
                
                <!-- Show current photo if exists -->
                {% if comment.photo %}
                <div class="mb-2">
                    <small class="text-muted">Current photo:</small><br>
                    <img src="{{ comment.photo.url }}" alt="Current comment photo" 
                         class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                </div>
                {% endif %}
                
                {{ form.photo }}
                <div class="form-text">JPG files only, max 10MB. Leave empty to keep current photo.</div>
                {% if form.photo.errors %}
                    <div class="text-danger small">{{ form.photo.errors.0 }}</div>
                {% endif %}
            </div>
            
            <!-- Form Actions -->
            <div class="d-flex justify-content-between">
                <div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>Save Changes
                    </button>
                    <a href="{% url 'inventory:item_by_code' item_code=item.item_code %}" class="btn btn-secondary ms-2">
                        <i class="fas fa-times me-1"></i>Cancel
                    </a>
                </div>
                
                <div class="text-muted small">
                    <i class="fas fa-info-circle me-1"></i>
                    Original comment posted on {{ comment.created_at|date:"M j, Y g:i A" }}
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Comment Preview -->
<div class="card mt-4">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="fas fa-eye me-2"></i>Current Comment
        </h6>
    </div>
    <div class="card-body">
        <div class="comment-item">
            <div class="d-flex justify-content-between align-items-start mb-2">
                <div class="d-flex align-items-center">
                    <i class="fas fa-user-check text-primary me-2" title="Authenticated User"></i>
                    <strong>{{ comment.commenter_name }}</strong>
                    <span class="badge bg-primary ms-2">User</span>
                    
                    {% if comment.reason %}
                        <span class="badge bg-warning text-dark ms-2">
                            <i class="fas fa-tag me-1"></i>{{ comment.reason.value }}
                        </span>
                    {% endif %}
                </div>
                
                <div class="text-muted small">
                    {{ comment.created_at|date:"M j, Y g:i A" }}
                    {% if comment.is_edited %}
                        <span class="text-muted">(edited)</span>
                    {% endif %}
                </div>
            </div>
            
            <div class="comment-text">
                {{ comment.comment_text|linebreaks }}
            </div>
            
            {% if comment.photo %}
            <div class="comment-photo mt-2">
                <img src="{{ comment.photo.url }}" alt="Comment photo" 
                     class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
