{% extends 'base.html' %}

{% block title %}Archive {{ item.item_name }} - Inventory Management{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header bg-warning">
                <h5 class="card-title">Confirm Archive</h5>
            </div>
            <div class="card-body">
                <h4 class="mb-3">Are you sure you want to archive this item?</h4>
                
                <div class="mb-4 p-3 border rounded">
                    <div class="mb-2">
                        <strong>Item Code:</strong> <code>{{ item.item_code }}</code>
                    </div>
                    <div class="mb-2">
                        <strong>Name:</strong> {{ item.item_name }}
                    </div>
                    <div>
                        <strong>Type:</strong> {{ item.item_type.value }}
                    </div>
                </div>
                
                <div class="alert alert-warning">
                    <p>
                        <strong>Note:</strong> Archiving an item will hide it from most views, but the record will still exist in the database.
                    </p>
                    {% if item.contained_items.exists %}
                    <p class="mb-0">
                        <strong>Warning:</strong> This item contains {{ item.contained_item_count }} other items. 
                        These contained items will NOT be automatically archived.
                    </p>
                    {% endif %}
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'inventory:item_detail' pk=item.pk %}" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-warning">
                            Confirm Archive
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
