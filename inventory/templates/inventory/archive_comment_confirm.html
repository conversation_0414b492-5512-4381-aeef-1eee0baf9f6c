{% extends 'base.html' %}

{% block title %}Archive Comment - {{ item.item_name }} - Inventory Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-archive me-2"></i>Archive Comment</h1>
    <a href="{% url 'inventory:item_by_code' item_code=item.item_code %}" class="btn btn-outline-primary">
        <i class="fas fa-arrow-left me-1"></i>Back to Item
    </a>
</div>

<!-- Item Context -->
<div class="card mb-4">
    <div class="card-body">
        <div class="d-flex align-items-center">
            <i class="fas fa-box me-2 text-muted"></i>
            <span class="text-muted me-2">Archiving comment on:</span>
            <a href="{% url 'inventory:item_by_code' item_code=item.item_code %}" class="text-decoration-none">
                <strong>{{ item.item_name }}</strong>
                <code class="ms-2">{{ item.item_code }}</code>
            </a>
        </div>
    </div>
</div>

<!-- Confirmation -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-exclamation-triangle text-warning me-2"></i>Confirm Archive Action
        </h5>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <h6 class="alert-heading">
                <i class="fas fa-info-circle me-1"></i>What happens when you archive this comment?
            </h6>
            <ul class="mb-0">
                <li>The comment will be hidden from the default view on item detail pages</li>
                <li>The comment will be hidden from the recent comments page by default</li>
                <li>The comment can still be viewed by toggling "Show Archived" option</li>
                <li>The comment can be unarchived later if needed</li>
                <li>This action will be logged for audit purposes</li>
            </ul>
        </div>
        
        <!-- Comment Preview -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">Comment to Archive</h6>
            </div>
            <div class="card-body">
                <div class="comment-item">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div class="d-flex align-items-center">
                            {% if comment.is_guest_comment %}
                                <i class="fas fa-user-circle text-muted me-2" title="Guest User"></i>
                                <strong>{{ comment.commenter_name }}</strong>
                                <span class="badge bg-light text-dark ms-2">Guest</span>
                            {% else %}
                                <i class="fas fa-user-check text-primary me-2" title="Authenticated User"></i>
                                <strong>{{ comment.commenter_name }}</strong>
                                <span class="badge bg-primary ms-2">User</span>
                            {% endif %}
                            
                            {% if comment.reason %}
                                <span class="badge bg-warning text-dark ms-2">
                                    <i class="fas fa-tag me-1"></i>{{ comment.reason.value }}
                                </span>
                            {% endif %}
                        </div>
                        
                        <div class="text-muted small">
                            {{ comment.created_at|date:"M j, Y g:i A" }}
                            {% if comment.is_edited %}
                                <span class="text-muted">(edited)</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="comment-text">
                        {{ comment.comment_text|linebreaks }}
                    </div>
                    
                    {% if comment.photo %}
                    <div class="comment-photo mt-2">
                        <img src="{{ comment.photo.url }}" alt="Comment photo" 
                             class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="d-flex justify-content-between mt-4">
            <div>
                <form method="post" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-archive me-1"></i>Yes, Archive Comment
                    </button>
                </form>
                <a href="{% url 'inventory:item_by_code' item_code=item.item_code %}" class="btn btn-secondary ms-2">
                    <i class="fas fa-times me-1"></i>Cancel
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
