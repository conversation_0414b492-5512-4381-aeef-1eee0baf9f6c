<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Small QR Labels</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: white;
        }

        .small-qr-sheet {
            /* No outer padding - let printer handle centering */
            width: 8in; /* Slightly smaller than full page */
            height: 10.25in; /* Slightly smaller than full page */
            margin: 0 auto; /* Center the sheet */
        }

        table.label-grid {
            border-collapse: separate;
            border-spacing: 0;
            table-layout: fixed;
            width: 100%;
        }

        table.label-grid td {
            width: 0.5in;
            height: 0.5in;
            padding: 0;
            padding-right: 0.125in;
            padding-bottom: 0.125in;
            vertical-align: middle;
            text-align: center;
        }

        /* Remove extra spacing from last column and row */
        table.label-grid td:last-child {
            padding-right: 0;
        }

        table.label-grid tr:last-child td {
            padding-bottom: 0;
        }

        .small-qr-label {
            width: 0.5in;
            height: 0.5in;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 0.05in;
            box-sizing: border-box;
        }

        .small-qrcode-image {
            width: 0.28in;
            height: 0.28in;
            margin-bottom: 0.00in;
        }

        .small-code {
            font-size: 6pt;
            color: red;
            boarder: 1px solid green;
            line-height: 1;
            font-family: monospace;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
</head>
<body>
    <div class="small-qr-sheet">
        <table class="label-grid">
            {% for row in label_rows %}
            <tr>
                {% for label in row %}
                <td>
                    <div class="small-qr-label">
                        {% if not label.is_blank %}
                        <img src="data:image/png;base64,{{ label.qrcode }}" alt="QR Code for {{ label.code }}" class="small-qrcode-image">
                        <div class="small-code">{{ label.code }}</div>
                        {% endif %}
                    </div>
                </td>
                {% endfor %}
            </tr>
            {% endfor %}
        </table>
    </div>
</body>
</html>
