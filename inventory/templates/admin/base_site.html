{% extends "admin/base_site.html" %}
{% load i18n %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block branding %}
<h1 id="site-name"><a href="{% url 'admin:index' %}">{{ site_header|default:_('Django administration') }}</a></h1>
{% endblock %}

{% block usertools %}
<div id="org-selector" style="float: right; margin-right: 20px;">
  <form method="get" style="display: inline-flex; align-items: center;">
    <label for="org-select" style="margin-right: 10px;">Organization:</label>
    <select id="org-select" name="org_id" onchange="this.form.submit()">
      {% for org in organizations %}
        <option value="{{ org.id }}" {% if org.id|stringformat:"i" == current_org_id %}selected{% endif %}>
          {{ org.name }}
        </option>
      {% endfor %}
    </select>
  </form>
</div>
{{ block.super }}
{% endblock %}

{% block nav-global %}{% endblock %}
