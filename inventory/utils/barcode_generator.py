"""
Barcode generation utilities for EPC data visualization.

This module provides functions to generate various types of barcodes from EPC
(Electronic Product Code) data, with support for different barcode formats
optimized for different use cases.
"""

import io
import logging
from typing import Optional, Tuple, Union
from PIL import Image
import barcode
from barcode.writer import ImageWriter
import qrcode
from qrcode.image.pil import PilImage

# Try to import DataMatrix support, fall back gracefully if not available
try:
    from pylibdmtx import pylibdmtx
    DATAMATRIX_AVAILABLE = True
except ImportError:
    DATAMATRIX_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("DataMatrix support not available. Install pylibdmtx and libdmtx for DataMatrix barcode support.")

logger = logging.getLogger(__name__)


class BarcodeFormat:
    """Supported barcode formats with their characteristics."""
    DATAMATRIX = 'datamatrix'
    QR_CODE = 'qr'
    CODE128 = 'code128'  # Moved to last as fallback

    # Format characteristics for UI display
    FORMATS = {
        DATAMATRIX: {
            'name': 'DataMatrix',
            'type': '2d',
            'description': '2D barcode, compact and reliable for hex data',
            'max_length': 3116,  # ASCII mode
        },
        QR_CODE: {
            'name': 'QR Code',
            'type': '2d',
            'description': '2D barcode, reliable for longer data',
            'max_length': 4296,  # Alphanumeric mode
        },
        CODE128: {
            'name': 'Code 128',
            'type': 'linear',
            'description': 'Linear barcode, fallback for compatibility',
            'max_length': 80,  # Practical limit for readability
        }
    }


def get_optimal_barcode_format(data: str) -> str:
    """
    Determine the optimal barcode format based on data characteristics.
    Prefers 2D barcodes over linear barcodes for better user experience.

    Args:
        data: The data to encode (typically EPC hex string)

    Returns:
        str: The recommended barcode format
    """
    data_length = len(data)

    # For EPC data, prefer 2D barcodes for better readability and user experience
    # DataMatrix is optimal for hex data - compact and reliable (if available)
    if (DATAMATRIX_AVAILABLE and
        data_length <= BarcodeFormat.FORMATS[BarcodeFormat.DATAMATRIX]['max_length']):
        return BarcodeFormat.DATAMATRIX
    # QR Code for longer data or as fallback if DataMatrix not available
    elif data_length <= BarcodeFormat.FORMATS[BarcodeFormat.QR_CODE]['max_length']:
        return BarcodeFormat.QR_CODE
    # Code128 as fallback for very long data (though unlikely for EPC)
    else:
        return BarcodeFormat.CODE128


def generate_datamatrix_barcode(data: str, size: int = 200) -> io.BytesIO:
    """
    Generate a DataMatrix barcode image.

    Args:
        data: Data to encode
        size: Image size in pixels (square)

    Returns:
        io.BytesIO: PNG image data

    Raises:
        ValueError: If data cannot be encoded in DataMatrix or library not available
    """
    if not DATAMATRIX_AVAILABLE:
        raise ValueError("DataMatrix support not available. Install pylibdmtx and libdmtx.")

    try:
        # Encode data to DataMatrix
        encoded = pylibdmtx.encode(data.encode('utf-8'))

        if not encoded:
            raise ValueError("Failed to encode data")

        # Convert to PIL Image
        img = Image.frombytes('RGB', (encoded.width, encoded.height), encoded.pixels)

        # Resize to target size while maintaining aspect ratio
        if img.size[0] != size or img.size[1] != size:
            # DataMatrix is always square, so we can resize directly
            img = img.resize((size, size), Image.Resampling.NEAREST)

        # Save to BytesIO
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        buffer.seek(0)

        return buffer

    except Exception as e:
        logger.error(f"Failed to generate DataMatrix barcode for data '{data}': {e}")
        raise ValueError(f"Cannot generate DataMatrix barcode: {e}")


def generate_code128_barcode(data: str, width: int = 2, height: int = 60) -> io.BytesIO:
    """
    Generate a Code 128 barcode image.
    
    Args:
        data: Data to encode
        width: Bar width in pixels
        height: Bar height in pixels
        
    Returns:
        io.BytesIO: PNG image data
        
    Raises:
        ValueError: If data cannot be encoded in Code 128
    """
    try:
        # Create Code 128 barcode
        code128_class = barcode.get_barcode_class('code128')
        
        # Configure image writer with custom dimensions
        writer = ImageWriter()
        writer.set_options({
            'module_width': width / 10.0,  # Convert to mm (approximate)
            'module_height': height / 10.0,
            'quiet_zone': 6.5,  # Standard quiet zone
            'font_size': 10,
            'text_distance': 5,
        })
        
        # Generate barcode
        barcode_instance = code128_class(data, writer=writer)
        
        # Render to BytesIO
        buffer = io.BytesIO()
        barcode_instance.write(buffer)
        buffer.seek(0)
        
        return buffer
        
    except Exception as e:
        logger.error(f"Failed to generate Code 128 barcode for data '{data}': {e}")
        raise ValueError(f"Cannot generate Code 128 barcode: {e}")


def generate_qr_code(data: str, size: int = 200) -> io.BytesIO:
    """
    Generate a QR code image.
    
    Args:
        data: Data to encode
        size: Image size in pixels (square)
        
    Returns:
        io.BytesIO: PNG image data
        
    Raises:
        ValueError: If data cannot be encoded in QR code
    """
    try:
        # Create QR code with optimal settings for hex data
        qr = qrcode.QRCode(
            version=1,  # Start with smallest version, will auto-adjust
            error_correction=qrcode.constants.ERROR_CORRECT_M,  # Medium error correction
            box_size=max(1, size // 25),  # Adjust box size based on target size
            border=4,  # Standard border
        )
        
        qr.add_data(data)
        qr.make(fit=True)
        
        # Generate image
        img = qr.make_image(image_factory=PilImage, fill_color="black", back_color="white")
        
        # Resize to target size if needed
        if img.size[0] != size:
            img = img.resize((size, size), Image.Resampling.NEAREST)
        
        # Save to BytesIO
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        buffer.seek(0)
        
        return buffer
        
    except Exception as e:
        logger.error(f"Failed to generate QR code for data '{data}': {e}")
        raise ValueError(f"Cannot generate QR code: {e}")


def generate_barcode(
    data: str,
    format_type: Optional[str] = None,
    **kwargs
) -> Tuple[io.BytesIO, str, dict]:
    """
    Generate a barcode image from EPC data.
    
    Args:
        data: EPC data (typically hex string)
        format_type: Barcode format (auto-detected if None)
        **kwargs: Format-specific options
        
    Returns:
        Tuple of (image_buffer, format_used, metadata)
        
    Raises:
        ValueError: If barcode cannot be generated
    """
    if not data:
        raise ValueError("Data cannot be empty")
    
    # Auto-detect format if not specified
    if format_type is None:
        format_type = get_optimal_barcode_format(data)
    
    # Validate format
    if format_type not in BarcodeFormat.FORMATS:
        raise ValueError(f"Unsupported barcode format: {format_type}")
    
    # Generate barcode based on format
    try:
        if format_type == BarcodeFormat.DATAMATRIX:
            if not DATAMATRIX_AVAILABLE:
                # Fall back to QR code if DataMatrix not available
                logger.warning(f"DataMatrix requested but not available, falling back to QR code")
                format_type = BarcodeFormat.QR_CODE
                buffer = generate_qr_code(
                    data,
                    size=kwargs.get('size', 200)
                )
            else:
                buffer = generate_datamatrix_barcode(
                    data,
                    size=kwargs.get('size', 200)
                )
        elif format_type == BarcodeFormat.QR_CODE:
            buffer = generate_qr_code(
                data,
                size=kwargs.get('size', 200)
            )
        elif format_type == BarcodeFormat.CODE128:
            buffer = generate_code128_barcode(
                data,
                width=kwargs.get('width', 2),
                height=kwargs.get('height', 60)
            )
        else:
            raise ValueError(f"Format {format_type} not implemented")
        
        # Prepare metadata
        metadata = {
            'format': format_type,
            'format_info': BarcodeFormat.FORMATS[format_type],
            'data_length': len(data),
            'generated': True,
        }
        
        return buffer, format_type, metadata
        
    except Exception as e:
        logger.error(f"Barcode generation failed for format {format_type}: {e}")
        raise


def generate_epc_barcode(
    epc_hex: str,
    format_type: Optional[str] = None,
    include_text: bool = True,
    **kwargs
) -> Tuple[io.BytesIO, str, dict]:
    """
    Generate a barcode specifically for EPC data.
    
    Args:
        epc_hex: EPC as hexadecimal string (24 characters)
        format_type: Barcode format (auto-detected if None)
        include_text: Whether to include human-readable text
        **kwargs: Format-specific options
        
    Returns:
        Tuple of (image_buffer, format_used, metadata)
        
    Raises:
        ValueError: If EPC data is invalid or barcode cannot be generated
    """
    # Validate EPC format
    if not epc_hex:
        raise ValueError("EPC data cannot be empty")
    
    # Clean and validate hex string
    clean_epc = epc_hex.replace(' ', '').upper()
    if len(clean_epc) != 24:
        raise ValueError(f"EPC must be 24 hex characters, got {len(clean_epc)}")
    
    if not all(c in '0123456789ABCDEF' for c in clean_epc):
        raise ValueError("EPC contains invalid hex characters")
    
    # Generate barcode
    buffer, format_used, metadata = generate_barcode(
        clean_epc,
        format_type=format_type,
        **kwargs
    )
    
    # Add EPC-specific metadata
    metadata.update({
        'epc_original': epc_hex,
        'epc_clean': clean_epc,
        'is_epc': True,
    })
    
    return buffer, format_used, metadata
