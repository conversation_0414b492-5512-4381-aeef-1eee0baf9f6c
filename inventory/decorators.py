from django.shortcuts import redirect
from django.contrib import messages
from functools import wraps
from .models import OrganizationUser

def organization_permission_required(permission_type='view'):
    """
    Decorator to check if user has the required permission in the current organization
    permission_type can be 'view', 'add', 'edit', or 'admin'
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            # Get current organization from session
            organization_id = request.session.get('org_id')
            
            if not organization_id:
                messages.warning(request, "Please select an organization first")
                return redirect('inventory:select_organization')
            
            # Check if user has permission in this organization
            try:
                org_user = OrganizationUser.objects.get(
                    user=request.user,
                    organization_id=organization_id
                )
                
                # Check specific permission
                if permission_type == 'admin' and not org_user.is_admin:
                    messages.error(request, "You don't have admin permission in this organization")
                    return redirect('inventory:item_list')
                elif permission_type == 'edit' and not org_user.can_edit:
                    messages.error(request, "You don't have edit permission in this organization")
                    return redirect('inventory:item_list')
                elif permission_type == 'add' and not org_user.can_add:
                    messages.error(request, "You don't have permission to add items in this organization")
                    return redirect('inventory:item_list')
                
                # Permission granted, proceed with the view
                return view_func(request, *args, **kwargs)
                
            except OrganizationUser.DoesNotExist:
                messages.error(request, "You don't have access to this organization")
                return redirect('inventory:select_organization')
                
        return _wrapped_view
    return decorator