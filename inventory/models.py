import secrets
from django.db import models
from django.core.exceptions import ValidationError
from django.urls import reverse
from django.core.validators import RegexValidator


class ManagedListValue(models.Model):
    """Stores controlled vocabularies for ItemTypes, ItemStatuses, and CommentReasons"""
    LIST_NAME_CHOICES = [
        ('ItemTypes', 'Item Types'),
        ('ItemStatuses', 'Item Statuses'),
        ('CommentReasons', 'Comment Reasons'),
    ]
    
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE, related_name='list_values')
    list_name = models.CharField(max_length=50, choices=LIST_NAME_CHOICES)
    value = models.CharField(max_length=100)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        unique_together = ['organization', 'list_name', 'value']
        indexes = [
            models.Index(fields=['list_name']),
            models.Index(fields=['is_active']),
            models.Index(fields=['organization']),
        ]
    
    def __str__(self):
        return f"{self.value} ({self.list_name})"


class Organization(models.Model):
    """Represents a distinct organization in the system"""
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20, unique=True)
    is_active = models.BooleanField(default=True)
    
    # RFID prefix for EPC generation (stored as integer)
    rfid_prefix = models.PositiveSmallIntegerField(
        unique=True,
        null=True,  # Temporarily allow null for migration compatibility
        blank=True,
        help_text="Numeric prefix for RFID EPCs (0-4095)"
    )
    
    # Visual identity elements
    logo = models.ImageField(upload_to='org_logos/', null=True, blank=True)
    primary_color = models.CharField(max_length=7, default="#007bff", 
                                    help_text="Primary brand color in hex format (e.g. #007bff)")
    secondary_color = models.CharField(max_length=7, default="#6c757d",
                                      help_text="Secondary brand color in hex format (e.g. #6c757d)")
    
    # Add related field to access users
    users = models.ManyToManyField(
        'auth.User', 
        through='OrganizationUser',
        related_name='organizations'
    )
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        # Auto-assign rfid_prefix if not provided
        if self.rfid_prefix is None and not self.pk:
            # We'll assign the prefix after saving to get the pk
            super().save(*args, **kwargs)
            
            # Now assign the prefix based on pk
            self.assign_rfid_prefix()
            
            # Save again with the prefix
            return super().save(*args, **kwargs)
        else:
            return super().save(*args, **kwargs)
    
    def assign_rfid_prefix(self):
        """Assign a unique RFID prefix by finding the highest prefix in use and incrementing by 1"""
        # Skip if already has a prefix
        if self.rfid_prefix is not None:
            return
        
        # Get the highest existing prefix
        from django.db.models import Max
        highest = Organization.objects.aggregate(Max('rfid_prefix'))['rfid_prefix__max']
        
        # If no prefixes exist yet, start with 0
        if highest is None:
            self.rfid_prefix = 0
        else:
            # Otherwise, increment by 1
            self.rfid_prefix = highest + 1
    
    @property
    def rfid_prefix_hex(self):
        """Return the RFID prefix as a hex string with appropriate length"""
        if self.rfid_prefix is None:
            return None
            
        if self.rfid_prefix < 16:
            # Single hex digit (0-f)
            return format(self.rfid_prefix, 'x')
        elif self.rfid_prefix < 256:
            # Two hex digits (00-ff)
            return format(self.rfid_prefix, '02x')
        else:
            # Three hex digits (000-fff)
            return format(self.rfid_prefix, '03x')


class OrganizationUser(models.Model):
    """Represents a user's membership and permissions in an organization"""
    user = models.ForeignKey('auth.User', on_delete=models.CASCADE)
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE)
    is_admin = models.BooleanField(default=False)
    can_edit = models.BooleanField(default=True)
    can_add = models.BooleanField(default=True)
    
    class Meta:
        unique_together = ('user', 'organization')
        
    def __str__(self):
        return f"{self.user.username} in {self.organization.name}"

    
class Item(models.Model):
    """
    Represents a physical item in the inventory system.
    """
    # Core fields
    _item_code = models.BinaryField(max_length=3, unique=True, editable=False, db_column='item_code')

    # EPC storage field for pre-generated EPCs
    _epc = models.BinaryField(
        max_length=12,  # 96 bits = 12 bytes
        unique=True,
        editable=False,
        db_column='epc',
        null=True,
        blank=True,
        help_text='96-bit Electronic Product Code stored as binary'
    )


    @property
    def item_code(self):
        """Return the item code as a hexadecimal string"""
        if self._item_code:
            return self._item_code.hex()
        return None

    @item_code.setter
    def item_code(self, value):
        """Set the item code from a hexadecimal string"""
        if value:
            if isinstance(value, str):
                self._item_code = bytes.fromhex(value)
            else:
                self._item_code = value



    item_name = models.CharField(max_length=255)
    item_description = models.TextField(null=True, blank=True)
    
    # Relationships
    item_type = models.ForeignKey(
        ManagedListValue, 
        on_delete=models.PROTECT,
        related_name='items_of_type',
        limit_choices_to={'list_name': 'ItemTypes', 'is_active': True},
        null=True,
        blank=True
    )
    status = models.ForeignKey(
        ManagedListValue,
        on_delete=models.PROTECT,
        related_name='items_with_status',
        limit_choices_to={'list_name': 'ItemStatuses', 'is_active': True},
        null=True,
        blank=True
    )
    located_in = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='contained_items'
    )
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='items')
    
    # Additional data
    # images = models.JSONField(default=list)  # Keep temporarily for migration
    custom_fields = models.JSONField(default=dict)
    
    # Metadata
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    is_archived = models.BooleanField(default=False)
    
    # Add direct image field to Item model
    image = models.ImageField(upload_to='item_images/', null=True, blank=True)
    image_caption = models.CharField(max_length=255, blank=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['_item_code']),  # Changed from 'item_code'
            models.Index(fields=['_epc']),  # Index for EPC search performance
            models.Index(fields=['located_in']),
            models.Index(fields=['is_archived']),
            models.Index(fields=['item_type']),
            models.Index(fields=['status']),
            models.Index(fields=['date_added']),
            models.Index(fields=['last_updated']),
        ]
        # Add constraints to ensure items belong to valid organizations
        constraints = [
            models.CheckConstraint(
                check=models.Q(organization__isnull=False),
                name='item_must_have_organization'
            )
        ]
    
    def __str__(self):
        return f"{self.item_name} ({self.item_code})"
    
    def validate_location_hierarchy(self, new_location):
        """Prevent circular references in the location hierarchy"""
        if not new_location:
            return True
        
        # Check for direct self-reference
        if self.pk and self.pk == new_location.pk:
            raise ValidationError("An item cannot be located inside itself.")
        
        # Check for circular reference
        current = new_location
        visited = set()
        
        while current and current.located_in:
            if current.pk in visited:
                raise ValidationError("Circular reference detected in location hierarchy.")
            if current.located_in.pk == self.pk:
                raise ValidationError("This would create a circular reference in the location hierarchy.")
            visited.add(current.pk)
            current = current.located_in
        
        return True

    def save(self, *args, **kwargs):
        # Generate item_code if this is a new record
        if not self.pk and not self.item_code:
            self.item_code = self.generate_item_code()

        # Generate and store EPC if not already present
        if not self._epc and self.organization and self.organization.rfid_prefix is not None and self._item_code:
            epc_int = self.generate_epc()
            if epc_int is not None:
                # Convert 96-bit integer to 12-byte binary representation
                self._epc = epc_int.to_bytes(12, byteorder='big')

        # Validate location hierarchy
        self.validate_location_hierarchy(self.located_in)

        super().save(*args, **kwargs)
    
    def generate_item_code(self):
        """Generate a unique 3-byte binary value for the item code"""
        max_attempts = 10
        for attempt in range(max_attempts):
            # Generate a random 3-byte value
            code_bytes = secrets.token_bytes(3)
            
            # Check if this code already exists - use raw query to avoid type issues
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute(
                    "SELECT COUNT(*) FROM inventory_item WHERE item_code = %s",
                    [code_bytes]
                )
                if cursor.fetchone()[0] == 0:
                    return code_bytes
        
        # If we reached max attempts, raise an exception
        raise ValidationError("Failed to generate a unique item code after multiple attempts.")
    
    def get_absolute_url(self):
        """Get the URL for the item detail page using item code"""
        return reverse('inventory:item_by_code', args=[self.item_code])
    
    @property
    def contained_item_count(self):
        """Count the number of items located in this item"""
        return self.contained_items.filter(is_archived=False).count()
    
    @property
    def primary_image(self):
        """Return self as a compatible object for templates"""
        if not self.image:
            return None
        return self
    
    @property
    def has_images(self):
        """Check if the item has an image"""
        return bool(self.image)
    
    # Make image URL accessible through the same interface as before
    @property
    def image_url(self):
        if self.image:
            return self.image.url
        return None
    
    def _reverse_bits(self, value, bit_count):
        """
        Reverse the bit order of a value within the specified bit count.

        Args:
            value (int): The value whose bits to reverse
            bit_count (int): Number of bits to consider (from LSB)

        Returns:
            int: Value with reversed bit order

        Example:
            _reverse_bits(0b101100001111, 12) -> 0b111100001101
        """
        result = 0
        for i in range(bit_count):
            if value & (1 << i):
                result |= (1 << (bit_count - 1 - i))
        return result

    def generate_epc(self):
        """
        Generate a 96-bit EPC (Electronic Product Code) on-the-fly.

        Structure (MSB to LSB):
        - Organization Prefix: 12 bits (from organization.rfid_prefix, bit-reversed)
        - Padding: 60 bits (reserved/padding space for future expansion)
        - Item Code: 24 bits (3 bytes from _item_code)

        The organization prefix bits are reversed to enable seamless expansion
        into the padding space for future growth scenarios.

        Returns:
            int: 96-bit EPC as an integer, or None if missing required data
        """
        try:
            if not self.organization or self.organization.rfid_prefix is None or not self._item_code:
                return None
        except Item.organization.RelatedObjectDoesNotExist:
            return None

        # Get organization prefix (12 bits max: 0-4095)
        org_prefix = self.organization.rfid_prefix & 0xFFF  # Ensure 12 bits

        # Reverse the bits of the organization prefix for expansion compatibility
        org_prefix_reversed = self._reverse_bits(org_prefix, 12)

        # Get item code as integer (24 bits from 3 bytes)
        item_code_int = int.from_bytes(self._item_code, byteorder='big') & 0xFFFFFF  # Ensure 24 bits

        # Padding is 60 bits of zeros (can be modified for future use)
        padding = 0

        # Combine: [Org Prefix (reversed): 12 bits][Padding: 60 bits][Item Code: 24 bits]
        epc = (org_prefix_reversed << 84) | (padding << 24) | item_code_int

        return epc

    @property
    def epc(self):
        """
        Return the EPC as a hexadecimal string for display.
        Uses stored EPC if available, otherwise generates on-the-fly.

        Returns:
            str: 24-character hexadecimal string (96 bits), or None if EPC cannot be generated
        """
        # Use stored EPC if available
        if self._epc:
            return self._epc.hex().upper()

        # Fall back to on-the-fly generation
        epc_int = self.generate_epc()
        if epc_int is None:
            return None

        # Format as 24-character hex string (96 bits = 12 bytes = 24 hex chars)
        return f"{epc_int:024x}".upper()

    @property
    def epc_hex(self):
        """
        Return the EPC as a hexadecimal string (alias for epc property).
        This provides compatibility with the API serializer.

        Returns:
            str: 24-character hexadecimal string (96 bits), or None if EPC cannot be generated
        """
        return self.epc

    @property
    def epc_formatted(self):
        """
        Return the EPC as a formatted hexadecimal string for better readability.

        Returns:
            str: Formatted EPC string with spaces every 4 characters, or None
        """
        epc_hex = self.epc
        if epc_hex is None:
            return None

        # Add spaces every 4 characters for readability
        return ' '.join(epc_hex[i:i+4] for i in range(0, len(epc_hex), 4))

    @classmethod
    def search_by_epc(cls, epc_query, organization=None):
        """
        Search for items by full or partial EPC.

        Args:
            epc_query (str): Full or partial EPC as hexadecimal string (case-insensitive)
            organization (Organization, optional): Limit search to specific organization

        Returns:
            QuerySet: Items matching the EPC query
        """
        if not epc_query:
            return cls.objects.none()

        # Clean the query - remove spaces and convert to lowercase
        clean_query = epc_query.replace(' ', '').lower()

        # Validate hex characters
        if not all(c in '0123456789abcdef' for c in clean_query):
            return cls.objects.none()

        # Build base queryset
        queryset = cls.objects.filter(_epc__isnull=False)
        if organization:
            queryset = queryset.filter(organization=organization)

        # For full EPC search (24 hex characters)
        if len(clean_query) == 24:
            try:
                epc_bytes = bytes.fromhex(clean_query)
                return queryset.filter(_epc=epc_bytes)
            except ValueError:
                return cls.objects.none()

        # For partial EPC search, we need to use raw SQL for efficient binary pattern matching
        from django.db import connection

        # Pad the query to even length for proper hex conversion
        if len(clean_query) % 2 == 1:
            clean_query += '0'

        try:
            # Convert partial hex to bytes
            partial_bytes = bytes.fromhex(clean_query)

            # Use LIKE with binary data for partial matching
            # This searches for the pattern at the beginning of the EPC
            with connection.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT id FROM inventory_item
                    WHERE epc IS NOT NULL
                    AND epc LIKE %s
                    """ + (" AND organization_id = %s" if organization else ""),
                    [partial_bytes + b'%'] + ([organization.id] if organization else [])
                )
                item_ids = [row[0] for row in cursor.fetchall()]

            return queryset.filter(id__in=item_ids)

        except ValueError:
            return cls.objects.none()

    @classmethod
    def search_by_epc_hex_pattern(cls, hex_pattern, organization=None):
        """
        Search for items by EPC using SQL LIKE pattern matching on hex representation.
        This method is useful for more complex pattern searches.

        Args:
            hex_pattern (str): Hex pattern with SQL wildcards (% and _)
            organization (Organization, optional): Limit search to specific organization

        Returns:
            QuerySet: Items matching the pattern
        """
        if not hex_pattern:
            return cls.objects.none()

        # Build base queryset
        queryset = cls.objects.filter(_epc__isnull=False)
        if organization:
            queryset = queryset.filter(organization=organization)

        # Use raw SQL to convert binary EPC to hex and apply pattern matching
        from django.db import connection

        with connection.cursor() as cursor:
            # Use PostgreSQL's encode function to convert binary to hex
            cursor.execute(
                """
                SELECT id FROM inventory_item
                WHERE epc IS NOT NULL
                AND UPPER(encode(epc, 'hex')) LIKE UPPER(%s)
                """ + (" AND organization_id = %s" if organization else ""),
                [hex_pattern] + ([organization.id] if organization else [])
            )
            item_ids = [row[0] for row in cursor.fetchall()]

        return queryset.filter(id__in=item_ids)

    def update_epc(self, force=False):
        """
        Update the stored EPC for this item.

        Args:
            force (bool): If True, regenerate EPC even if one already exists

        Returns:
            bool: True if EPC was updated, False otherwise
        """
        if not force and self._epc:
            return False

        if not self.organization or self.organization.rfid_prefix is None or not self._item_code:
            return False

        epc_int = self.generate_epc()
        if epc_int is not None:
            self._epc = epc_int.to_bytes(12, byteorder='big')
            self.save(update_fields=['_epc'])
            return True

        return False

    def clean(self):
        super().clean()


class ItemImage(models.Model):
    """Stores images associated with inventory items"""
    item = models.ForeignKey('Item', on_delete=models.CASCADE, related_name='item_images')
    image = models.ImageField(upload_to='item_images/')
    caption = models.CharField(max_length=255, blank=True)
    is_primary = models.BooleanField(default=False)
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order']

    def __str__(self):
        return f"Image for {self.item.item_name}"

    def save(self, *args, **kwargs):
        # If this is marked as primary, ensure no other image for this item is primary
        if self.is_primary:
            ItemImage.objects.filter(item=self.item, is_primary=True).exclude(pk=self.pk).update(is_primary=False)
        super().save(*args, **kwargs)


class ItemComment(models.Model):
    """Stores comments on inventory items from both authenticated and guest users"""

    # Core fields
    item = models.ForeignKey('Item', on_delete=models.CASCADE, related_name='comments')
    comment_text = models.TextField(help_text="Comment content")

    # User identification - either authenticated user OR guest name
    user = models.ForeignKey(
        'auth.User',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Authenticated user who posted the comment"
    )
    guest_name = models.CharField(
        max_length=100,
        blank=True,
        help_text="Name provided by guest user"
    )

    # Optional categorization
    reason = models.ForeignKey(
        ManagedListValue,
        on_delete=models.SET_NULL,
        related_name='comments_with_reason',
        limit_choices_to={'list_name': 'CommentReasons', 'is_active': True},
        null=True,
        blank=True,
        help_text="Optional reason category for the comment"
    )

    # Optional photo attachment
    photo = models.ImageField(
        upload_to='comment_photos/',
        null=True,
        blank=True,
        help_text="Optional photo attachment (JPG only, max 10MB)"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Editing tracking
    is_edited = models.BooleanField(default=False)

    # Archive/completion tracking
    is_archived = models.BooleanField(
        default=False,
        help_text="Mark comment as archived/completed to hide from default views"
    )
    archived_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Timestamp when comment was archived"
    )
    archived_by = models.ForeignKey(
        'auth.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='archived_comments',
        help_text="User who archived this comment"
    )

    class Meta:
        ordering = ['-created_at']  # Newest first
        indexes = [
            models.Index(fields=['item', '-created_at']),
            models.Index(fields=['-created_at']),  # For recent comments page
            models.Index(fields=['user']),
            models.Index(fields=['reason']),
            models.Index(fields=['is_archived']),  # For filtering archived comments
            models.Index(fields=['item', 'is_archived', '-created_at']),  # Compound index for item comments
        ]
        constraints = [
            # Ensure either user OR guest_name is provided, but not both
            models.CheckConstraint(
                check=(
                    models.Q(user__isnull=False, guest_name='') |
                    models.Q(user__isnull=True, guest_name__isnull=False) &
                    ~models.Q(guest_name='')
                ),
                name='comment_must_have_user_or_guest_name'
            )
        ]

    def __str__(self):
        commenter = self.user.username if self.user else f"Guest: {self.guest_name}"
        return f"Comment by {commenter} on {self.item.item_name}"

    @property
    def commenter_name(self):
        """Return the name of the commenter (authenticated user or guest)"""
        if self.user:
            return self.user.get_full_name() or self.user.username
        return self.guest_name

    @property
    def is_guest_comment(self):
        """Check if this is a guest comment"""
        return self.user is None

    @property
    def can_edit(self, request_user=None):
        """Check if a user can edit this comment"""
        if self.user and request_user:
            return self.user == request_user
        # Guest comments can't be edited after creation for now
        return False

    def can_archive(self, request_user=None):
        """Check if a user can archive this comment"""
        # For now, any authenticated user can archive any comment
        # This can be extended for more granular permissions later
        return request_user and request_user.is_authenticated

    def archive(self, archived_by_user):
        """Archive this comment"""
        from django.utils import timezone
        import logging

        if not self.is_archived:
            self.is_archived = True
            self.archived_at = timezone.now()
            self.archived_by = archived_by_user
            self.save(update_fields=['is_archived', 'archived_at', 'archived_by'])

            # Log the archive action
            logger = logging.getLogger('inventory.comments')
            logger.info(
                f"Comment archived: ID={self.id}, Item={self.item.item_code}, "
                f"ArchivedBy={archived_by_user.username}, "
                f"OriginalCommenter={'Guest:' + self.guest_name if self.is_guest_comment else self.user.username}"
            )

            return True
        return False

    def unarchive(self):
        """Unarchive this comment"""
        import logging

        if self.is_archived:
            # Store info for logging before clearing
            archived_by_username = self.archived_by.username if self.archived_by else 'Unknown'

            self.is_archived = False
            self.archived_at = None
            self.archived_by = None
            self.save(update_fields=['is_archived', 'archived_at', 'archived_by'])

            # Log the unarchive action
            logger = logging.getLogger('inventory.comments')
            logger.info(
                f"Comment unarchived: ID={self.id}, Item={self.item.item_code}, "
                f"PreviouslyArchivedBy={archived_by_username}, "
                f"OriginalCommenter={'Guest:' + self.guest_name if self.is_guest_comment else self.user.username}"
            )

            return True
        return False

    @property
    def archive_status_display(self):
        """Return human-readable archive status"""
        if self.is_archived:
            return f"Archived on {self.archived_at.strftime('%b %d, %Y')} by {self.archived_by.username if self.archived_by else 'Unknown'}"
        return "Active"

    def clean(self):
        """Validate that either user or guest_name is provided"""
        from django.core.exceptions import ValidationError

        if self.user and self.guest_name:
            raise ValidationError("Comment cannot have both authenticated user and guest name")

        if not self.user and not self.guest_name:
            raise ValidationError("Comment must have either authenticated user or guest name")

        # Validate photo file type if provided
        if self.photo:
            import os
            file_extension = os.path.splitext(self.photo.name)[1].lower()
            if file_extension not in ['.jpg', '.jpeg']:
                raise ValidationError("Photo must be a JPG file")

    def save(self, *args, **kwargs):
        # Run validation
        self.clean()

        # Compress photo if provided
        if self.photo:
            self._compress_photo()

        super().save(*args, **kwargs)

    def _compress_photo(self):
        """Compress the uploaded photo to reduce file size"""
        from PIL import Image
        from io import BytesIO
        from django.core.files.base import ContentFile
        import os

        # Open the image
        img = Image.open(self.photo)

        # Convert to RGB if necessary (for JPEG)
        if img.mode in ('RGBA', 'LA', 'P'):
            img = img.convert('RGB')

        # Calculate new size (max 1920x1920 while maintaining aspect ratio)
        max_size = 1920
        if img.width > max_size or img.height > max_size:
            img.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)

        # Save to BytesIO with compression
        output = BytesIO()
        img.save(output, format='JPEG', quality=85, optimize=True)
        output.seek(0)

        # Replace the file
        filename = os.path.splitext(self.photo.name)[0] + '.jpg'
        self.photo.save(
            filename,
            ContentFile(output.read()),
            save=False
        )
