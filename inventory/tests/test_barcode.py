"""
Tests for barcode generation functionality.
"""

import io
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from inventory.models import Item, Organization, ManagedListValue, OrganizationUser
from inventory.utils.barcode_generator import (
    generate_epc_barcode, 
    generate_code128_barcode, 
    generate_qr_code,
    get_optimal_barcode_format,
    BarcodeFormat
)


class BarcodeGeneratorTestCase(TestCase):
    """Test barcode generation utilities."""
    
    def setUp(self):
        """Set up test data."""
        self.test_epc = "FC0000000000000123456"  # 24 hex chars
        self.test_epc_formatted = "FC00 0000 0000 0001 2345 6"
        
    def test_get_optimal_barcode_format(self):
        """Test optimal format selection."""
        # Short data should use Code128
        short_data = "123456"
        self.assertEqual(get_optimal_barcode_format(short_data), BarcodeFormat.CODE128)
        
        # Very long data should use QR
        long_data = "A" * 100
        self.assertEqual(get_optimal_barcode_format(long_data), BarcodeFormat.QR_CODE)
        
    def test_generate_code128_barcode(self):
        """Test Code128 barcode generation."""
        buffer = generate_code128_barcode(self.test_epc)
        self.assertIsInstance(buffer, io.BytesIO)
        self.assertGreater(len(buffer.getvalue()), 0)
        
    def test_generate_qr_code(self):
        """Test QR code generation."""
        buffer = generate_qr_code(self.test_epc)
        self.assertIsInstance(buffer, io.BytesIO)
        self.assertGreater(len(buffer.getvalue()), 0)
        
    def test_generate_epc_barcode_valid(self):
        """Test EPC barcode generation with valid data."""
        buffer, format_used, metadata = generate_epc_barcode(self.test_epc)
        
        self.assertIsInstance(buffer, io.BytesIO)
        self.assertIn(format_used, [BarcodeFormat.CODE128, BarcodeFormat.QR_CODE])
        self.assertIsInstance(metadata, dict)
        self.assertTrue(metadata['is_epc'])
        self.assertEqual(metadata['epc_clean'], self.test_epc.upper())
        
    def test_generate_epc_barcode_formatted_input(self):
        """Test EPC barcode generation with formatted input."""
        buffer, format_used, metadata = generate_epc_barcode(self.test_epc_formatted)
        
        self.assertIsInstance(buffer, io.BytesIO)
        self.assertEqual(metadata['epc_clean'], self.test_epc.replace(' ', '').upper())
        
    def test_generate_epc_barcode_invalid_length(self):
        """Test EPC barcode generation with invalid length."""
        with self.assertRaises(ValueError):
            generate_epc_barcode("123")  # Too short
            
        with self.assertRaises(ValueError):
            generate_epc_barcode("A" * 30)  # Too long
            
    def test_generate_epc_barcode_invalid_hex(self):
        """Test EPC barcode generation with invalid hex characters."""
        with self.assertRaises(ValueError):
            generate_epc_barcode("GGGGGGGGGGGGGGGGGGGGGGGG")  # Invalid hex
            
    def test_generate_epc_barcode_empty(self):
        """Test EPC barcode generation with empty data."""
        with self.assertRaises(ValueError):
            generate_epc_barcode("")


class BarcodeViewTestCase(TestCase):
    """Test barcode view functionality."""
    
    def setUp(self):
        """Set up test data."""
        # Create organization
        self.organization = Organization.objects.create(
            name="Test Org",
            code="TEST",
            rfid_prefix=255
        )
        
        # Create user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        # Create organization user
        OrganizationUser.objects.create(
            user=self.user,
            organization=self.organization,
            is_admin=True
        )
        
        # Create managed list values
        self.item_type = ManagedListValue.objects.create(
            organization=self.organization,
            list_name='ItemTypes',
            value='Test Type'
        )
        
        self.item_status = ManagedListValue.objects.create(
            organization=self.organization,
            list_name='ItemStatuses',
            value='Active'
        )
        
        # Create test item
        self.item = Item.objects.create(
            item_name="Test Item",
            item_description="Test Description",
            organization=self.organization,
            item_type=self.item_type,
            status=self.item_status
        )
        
        self.client = Client()
        
    def test_item_barcode_view_success(self):
        """Test successful barcode generation."""
        url = reverse('inventory:item_barcode', kwargs={'item_code': self.item.item_code})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'image/png')
        self.assertIn('X-Barcode-Format', response)
        self.assertIn('X-EPC-Data', response)
        
    def test_item_barcode_view_with_format(self):
        """Test barcode generation with specific format."""
        url = reverse('inventory:item_barcode', kwargs={'item_code': self.item.item_code})
        response = self.client.get(url, {'format': 'code128'})
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['X-Barcode-Format'], 'code128')
        
    def test_item_barcode_view_with_size(self):
        """Test barcode generation with custom size."""
        url = reverse('inventory:item_barcode', kwargs={'item_code': self.item.item_code})
        response = self.client.get(url, {'size': '150'})
        
        self.assertEqual(response.status_code, 200)
        
    def test_item_barcode_view_invalid_item(self):
        """Test barcode view with invalid item code."""
        url = reverse('inventory:item_barcode', kwargs={'item_code': 'INVALID'})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 404)
        
    def test_item_barcode_view_no_epc(self):
        """Test barcode view for item without EPC."""
        # Create item without EPC (no organization rfid_prefix)
        org_no_rfid = Organization.objects.create(
            name="No RFID Org",
            code="NORFID"
        )
        
        item_no_epc = Item.objects.create(
            item_name="No EPC Item",
            organization=org_no_rfid,
            item_type=self.item_type,
            status=self.item_status
        )
        
        url = reverse('inventory:item_barcode', kwargs={'item_code': item_no_epc.item_code})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 404)
        
    def test_item_barcode_caching(self):
        """Test barcode response caching headers."""
        url = reverse('inventory:item_barcode', kwargs={'item_code': self.item.item_code})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('Cache-Control', response)
        self.assertIn('ETag', response)
        self.assertIn('max-age=3600', response['Cache-Control'])
