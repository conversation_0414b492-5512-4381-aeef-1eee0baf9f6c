from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.contrib.messages import get_messages
from ..models import Organization, OrganizationUser
import json


class OrganizationSwitchingTest(TestCase):
    """Test cases for organization switching functionality"""

    def setUp(self):
        """Set up test data"""
        self.client = Client()
        
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test organizations
        self.org1 = Organization.objects.create(
            name='Organization 1',
            code='ORG001',
            rfid_prefix=1
        )
        
        self.org2 = Organization.objects.create(
            name='Organization 2',
            code='ORG002',
            rfid_prefix=2
        )
        
        # Create organization user relationships
        OrganizationUser.objects.create(
            user=self.user,
            organization=self.org1,
            is_admin=True
        )
        
        OrganizationUser.objects.create(
            user=self.user,
            organization=self.org2,
            can_edit=True,
            can_add=True
        )

    def test_organization_switching_success(self):
        """Test successful organization switching"""
        self.client.login(username='testuser', password='testpass123')
        
        # Switch to organization 1
        response = self.client.post(
            reverse('inventory:switch_organization', args=[self.org1.id]),
            {'next': '/inventory/items/'}
        )
        
        # Should redirect properly
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, '/inventory/items/')
        
        # Check session has org_id
        session = self.client.session
        self.assertEqual(session.get('org_id'), str(self.org1.id))
        
        # Check success message
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('Switched to Organization 1' in str(m) for m in messages))

    def test_organization_switching_with_backup_cookie(self):
        """Test that backup cookie is set during organization switching"""
        self.client.login(username='testuser', password='testpass123')
        
        response = self.client.post(
            reverse('inventory:switch_organization', args=[self.org2.id]),
            {'next': '/inventory/items/'}
        )
        
        # Check backup cookie is set
        self.assertIn('backup_org_id', response.cookies)
        self.assertEqual(response.cookies['backup_org_id'].value, str(self.org2.id))

    def test_organization_switching_invalid_org(self):
        """Test organization switching with invalid organization ID"""
        self.client.login(username='testuser', password='testpass123')
        
        response = self.client.post(
            reverse('inventory:switch_organization', args=[999]),  # Non-existent org
            {'next': '/inventory/items/'}
        )
        
        # Should return 404
        self.assertEqual(response.status_code, 404)

    def test_organization_switching_unauthorized_org(self):
        """Test organization switching to unauthorized organization"""
        # Create organization user doesn't have access to
        org3 = Organization.objects.create(
            name='Organization 3',
            code='ORG003',
            rfid_prefix=3
        )
        
        self.client.login(username='testuser', password='testpass123')
        
        response = self.client.post(
            reverse('inventory:switch_organization', args=[org3.id]),
            {'next': '/inventory/items/'}
        )
        
        # Should return 404 (organization exists but user has no access)
        self.assertEqual(response.status_code, 404)

    def test_organization_selection_page_loads(self):
        """Test that organization selection page loads correctly"""
        self.client.login(username='testuser', password='testpass123')
        
        response = self.client.get(reverse('inventory:select_organization'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Organization 1')
        self.assertContains(response, 'Organization 2')
        self.assertContains(response, 'Select Organization')

    def test_logout_from_organization_selection(self):
        """Test logout functionality from organization selection page"""
        self.client.login(username='testuser', password='testpass123')
        
        response = self.client.post(reverse('inventory:logout'))
        
        # Should redirect to login page
        self.assertEqual(response.status_code, 302)
        self.assertIn('login', response.url)
        
        # Check success message
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('successfully logged out' in str(m) for m in messages))

    def test_session_persistence_after_switching(self):
        """Test that session persists after organization switching"""
        self.client.login(username='testuser', password='testpass123')
        
        # Switch organization
        self.client.post(
            reverse('inventory:switch_organization', args=[self.org1.id]),
            {'next': '/inventory/items/'}
        )
        
        # Make another request to verify session persists
        response = self.client.get('/inventory/debug-session/')
        self.assertEqual(response.status_code, 200)
        
        # Parse JSON response
        data = json.loads(response.content)
        self.assertEqual(data['org_id'], str(self.org1.id))
        self.assertTrue(data['is_authenticated'])
