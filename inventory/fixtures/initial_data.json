[{"model": "inventory.managedlistvalue", "pk": 1, "fields": {"list_name": "ItemTypes", "value": "Location - Room", "is_active": true}}, {"model": "inventory.managedlistvalue", "pk": 2, "fields": {"list_name": "ItemTypes", "value": "Location - Box", "is_active": true}}, {"model": "inventory.managedlistvalue", "pk": 3, "fields": {"list_name": "ItemTypes", "value": "Costume - Base", "is_active": true}}, {"model": "inventory.managedlistvalue", "pk": 4, "fields": {"list_name": "ItemTypes", "value": "Accessory", "is_active": true}}, {"model": "inventory.managedlistvalue", "pk": 5, "fields": {"list_name": "ItemTypes", "value": "Prop", "is_active": true}}, {"model": "inventory.managedlistvalue", "pk": 6, "fields": {"list_name": "ItemStatuses", "value": "Available", "is_active": true}}, {"model": "inventory.managedlistvalue", "pk": 7, "fields": {"list_name": "ItemStatuses", "value": "In Use", "is_active": true}}, {"model": "inventory.managedlistvalue", "pk": 8, "fields": {"list_name": "ItemStatuses", "value": "Needs Repair", "is_active": true}}, {"model": "inventory.managedlistvalue", "pk": 9, "fields": {"list_name": "ItemStatuses", "value": "Lost", "is_active": true}}]