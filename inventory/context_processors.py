from .models import Organization, OrganizationUser

def organization_context(request):
    """Add organizations to context for all templates"""
    organizations = Organization.objects.filter(is_active=True)
    
    # Add count of user's organizations if user is authenticated
    user_org_count = 0
    if request.user.is_authenticated:
        if request.user.is_superuser:
            user_org_count = organizations.count()
        else:
            user_org_count = OrganizationUser.objects.filter(
                user=request.user,
                organization__is_active=True
            ).count()
    
    return {
        'organizations': organizations,
        'user_org_count': user_org_count,
    }
