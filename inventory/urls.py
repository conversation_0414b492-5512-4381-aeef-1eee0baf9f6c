from django.urls import path
from django.views.generic import RedirectView
from . import views

app_name = 'inventory'

urlpatterns = [
    # Direct admin access
    path('admin-access/', RedirectView.as_view(url='/admin/'), name='admin_access'),

    # Item list and search
    path('items/', views.item_list, name='item_list'),

    # Item CRUD operations
    path('item/<str:item_code>/', views.item_by_code, name='item_by_code'),
    path('items/<int:pk>/', views.item_detail, name='item_detail'),
    path('items/add/', views.add_item, name='add_item'),
    path('items/add/<str:item_code>/', views.add_item_with_code, name='add_item_with_code'),
    path('item/<str:item_code>/edit/', views.edit_item_by_code, name='edit_item_by_code'),
    path('item/<str:item_code>/archive/', views.archive_item_by_code, name='archive_item_by_code'),
    path('item/<str:item_code>/move/', views.move_item, name='move_item_by_code'),
    path('items/<int:pk>/move/', views.move_item, name='move_item'),

    # Item lookup
    path('lookup/', views.item_lookup, name='item_lookup'),

    # AJAX item autocomplete
    path('api/items/autocomplete/', views.item_autocomplete, name='item_autocomplete'),

    # Barcode generation
    path('item/<str:item_code>/barcode/', views.item_barcode, name='item_barcode'),



    # Label generation
    path('labels/generate/', views.generate_labels, name='generate_labels'),

    # Test PDF generation
    path('test-pdf/', views.test_pdf, name='test_pdf'),

    # Test location picker
    path('test-location-picker/', views.test_location_picker, name='test_location_picker'),

    # Test static file serving
    path('test-static-file/', views.test_static_file, name='test_static_file'),

    # Safari-specific CSS test
    path('safari-test/', views.safari_test, name='safari_test'),

    # Comment-related URLs
    path('item/<str:item_code>/comment/add/', views.add_comment, name='add_comment'),
    path('comment/<int:comment_id>/edit/', views.edit_comment, name='edit_comment'),
    path('comment/<int:comment_id>/archive/', views.archive_comment, name='archive_comment'),
    path('comment/<int:comment_id>/unarchive/', views.unarchive_comment, name='unarchive_comment'),
    path('comments/recent/', views.recent_comments, name='recent_comments'),
    path('api/item/<str:item_code>/comments/', views.item_comments_ajax, name='item_comments_ajax'),

    # Organization management
    path('organization/select/', views.select_organization, name='select_organization'),
    path('organization/switch/<int:org_id>/', views.switch_organization, name='switch_organization'),
    path('organization/none/', views.no_organizations, name='no_organizations'),
    path('test-media-file/<str:filename>', views.serve_media_file, name='serve_media_file'),
    
    # Authentication
    path('logout/', views.logout_view, name='logout'),
    # Debug URLs
    path('debug-session/', views.debug_session, name='debug_session'),
]
