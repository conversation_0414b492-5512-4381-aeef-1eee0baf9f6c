"""
Management command to create API tokens for users.

This command helps create JWT tokens for testing and initial setup.
"""

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from inventory.models import OrganizationUser


class Command(BaseCommand):
    help = 'Create API token for a user'
    
    def add_arguments(self, parser):
        parser.add_argument(
            'username',
            type=str,
            help='Username to create token for'
        )
        parser.add_argument(
            '--organization',
            type=str,
            help='Organization code to include in token context'
        )
        parser.add_argument(
            '--show-refresh',
            action='store_true',
            help='Also show the refresh token'
        )
    
    def handle(self, *args, **options):
        username = options['username']
        organization_code = options.get('organization')
        show_refresh = options.get('show_refresh', False)
        
        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            raise CommandError(f'User "{username}" does not exist')
        
        # Check organization access if specified
        if organization_code:
            try:
                org_user = OrganizationUser.objects.get(
                    user=user,
                    organization__code=organization_code,
                    organization__is_active=True
                )
                self.stdout.write(
                    self.style.SUCCESS(
                        f'User has access to organization: {org_user.organization.name}'
                    )
                )
            except OrganizationUser.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(
                        f'User does not have access to organization "{organization_code}"'
                    )
                )
        
        # Create tokens
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token
        
        self.stdout.write(
            self.style.SUCCESS(f'Tokens created for user: {user.username}')
        )
        self.stdout.write(f'Access Token: {access_token}')
        
        if show_refresh:
            self.stdout.write(f'Refresh Token: {refresh}')
        
        # Show user's organizations
        user_orgs = OrganizationUser.objects.filter(
            user=user
        ).select_related('organization').filter(
            organization__is_active=True
        )
        
        if user_orgs:
            self.stdout.write('\nUser Organizations:')
            for org_user in user_orgs:
                permissions = []
                if org_user.is_admin:
                    permissions.append('admin')
                if org_user.can_edit:
                    permissions.append('edit')
                if org_user.can_add:
                    permissions.append('add')
                
                self.stdout.write(
                    f'  - {org_user.organization.name} ({org_user.organization.code}) '
                    f'[{", ".join(permissions)}]'
                )
        else:
            self.stdout.write(
                self.style.WARNING('User does not belong to any organizations')
            )
        
        # Show usage example
        self.stdout.write('\nUsage example:')
        self.stdout.write('curl -H "Authorization: Bearer <access_token>" \\')
        if organization_code:
            self.stdout.write(f'     -H "X-Organization-ID: {org_user.organization.id}" \\')
        self.stdout.write('     http://localhost:8000/api/v1/items/')
