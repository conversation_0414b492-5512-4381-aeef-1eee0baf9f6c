from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from inventory.models import Item, Organization


class Command(BaseCommand):
    help = 'Update EPCs for existing items'

    def add_arguments(self, parser):
        parser.add_argument(
            '--organization',
            type=str,
            help='Update EPCs only for items in the specified organization (by code)',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force update EPCs even if they already exist',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=1000,
            help='Number of items to process in each batch (default: 1000)',
        )

    def handle(self, *args, **options):
        organization_code = options.get('organization')
        force = options.get('force', False)
        dry_run = options.get('dry_run', False)
        batch_size = options.get('batch_size', 1000)

        # Build queryset
        queryset = Item.objects.select_related('organization')
        
        if organization_code:
            try:
                organization = Organization.objects.get(code=organization_code)
                queryset = queryset.filter(organization=organization)
                self.stdout.write(f"Filtering items for organization: {organization.name}")
            except Organization.DoesNotExist:
                raise CommandError(f"Organization with code '{organization_code}' does not exist")
        
        # Filter items that need EPC updates
        if force:
            # Update all items with valid data
            items_to_update = queryset.filter(
                organization__isnull=False,
                organization__rfid_prefix__isnull=False,
                _item_code__isnull=False
            )
        else:
            # Only update items without stored EPCs
            items_to_update = queryset.filter(
                _epc__isnull=True,
                organization__isnull=False,
                organization__rfid_prefix__isnull=False,
                _item_code__isnull=False
            )

        total_items = items_to_update.count()
        
        if total_items == 0:
            self.stdout.write(
                self.style.WARNING("No items found that need EPC updates.")
            )
            return

        self.stdout.write(f"Found {total_items} items to update")
        
        if dry_run:
            self.stdout.write(self.style.WARNING("DRY RUN - No changes will be made"))
            
            # Show sample of items that would be updated
            sample_items = items_to_update[:10]
            self.stdout.write("\nSample items that would be updated:")
            for item in sample_items:
                current_epc = item.epc if item._epc else "None"
                self.stdout.write(f"  - {item.item_name} ({item.item_code}): Current EPC = {current_epc}")
            
            if total_items > 10:
                self.stdout.write(f"  ... and {total_items - 10} more items")
            
            return

        # Perform the updates
        updated_count = 0
        error_count = 0
        
        self.stdout.write(f"Updating EPCs in batches of {batch_size}...")
        
        for i in range(0, total_items, batch_size):
            batch = items_to_update[i:i + batch_size]
            
            with transaction.atomic():
                for item in batch:
                    try:
                        if item.update_epc(force=force):
                            updated_count += 1
                        
                    except Exception as e:
                        error_count += 1
                        self.stdout.write(
                            self.style.ERROR(f"Error updating EPC for item {item.pk}: {e}")
                        )
            
            # Progress update
            processed = min(i + batch_size, total_items)
            self.stdout.write(f"Processed {processed}/{total_items} items...")

        # Summary
        self.stdout.write(
            self.style.SUCCESS(f"\nEPC update completed:")
        )
        self.stdout.write(f"  - Successfully updated: {updated_count} items")
        if error_count > 0:
            self.stdout.write(
                self.style.WARNING(f"  - Errors encountered: {error_count} items")
            )
        
        # Verification
        if updated_count > 0:
            self.stdout.write("\nVerifying updates...")
            verification_queryset = queryset.filter(_epc__isnull=False)
            if organization_code:
                verification_queryset = verification_queryset.filter(organization__code=organization_code)
            
            total_with_epcs = verification_queryset.count()
            self.stdout.write(f"Total items with stored EPCs: {total_with_epcs}")
            
            # Show sample of updated EPCs
            sample_updated = verification_queryset.order_by('-last_updated')[:5]
            self.stdout.write("\nSample updated EPCs:")
            for item in sample_updated:
                self.stdout.write(f"  - {item.item_name}: {item.epc_formatted}")
