from django.core.management.base import BaseCommand
from inventory.models import Organization, ManagedListValue


class Command(BaseCommand):
    help = 'Set up initial comment reasons for all organizations'

    def handle(self, *args, **options):
        # Define the initial comment reasons
        initial_reasons = [
            'Needs Repair',
        ]
        
        # Get all active organizations
        organizations = Organization.objects.filter(is_active=True)
        
        if not organizations.exists():
            self.stdout.write(
                self.style.WARNING('No active organizations found. Please create organizations first.')
            )
            return
        
        created_count = 0
        
        for org in organizations:
            self.stdout.write(f'Setting up comment reasons for organization: {org.name}')
            
            for reason in initial_reasons:
                # Check if this reason already exists for this organization
                existing = ManagedListValue.objects.filter(
                    organization=org,
                    list_name='CommentReasons',
                    value=reason
                ).exists()
                
                if not existing:
                    ManagedListValue.objects.create(
                        organization=org,
                        list_name='CommentReasons',
                        value=reason,
                        is_active=True
                    )
                    created_count += 1
                    self.stdout.write(f'  Created reason: {reason}')
                else:
                    self.stdout.write(f'  Reason already exists: {reason}')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully set up comment reasons. Created {created_count} new reason entries.'
            )
        )
