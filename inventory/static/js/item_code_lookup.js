/**
 * Item Code Lookup Widget
 *
 * This script provides autocomplete functionality for the item code lookup widget.
 * It allows users to search for items by code or name and select them.
 */
document.addEventListener('DOMContentLoaded', function() {
    // Find all item code lookup widgets
    const lookupInputs = document.querySelectorAll('.item-code-lookup');

    lookupInputs.forEach(function(input) {
        const inputId = input.id;
        const hiddenInput = document.getElementById(`${inputId}_hidden`);
        const displayField = document.getElementById(`${inputId}_display`);
        const clearButton = document.getElementById(`${inputId}_clear`);

        // Initialize the widget
        initializeWidget(input, hiddenInput, displayField, clearButton);

        // If there's an initial value, fetch the item details
        if (hiddenInput.value) {
            fetchItemDetails(hiddenInput.value, displayField, clearButton);
        }
    });

    /**
     * Initialize the widget with autocomplete and event handlers
     */
    function initializeWidget(input, hiddenInput, displayField, clearButton) {
        // Set up autocomplete
        $(input).autocomplete({
            source: function(request, response) {
                // Get the current item ID if we're editing an existing item
                const currentItemId = document.querySelector('input[name="current_item_id"]')?.value || '';

                // Make AJAX request to the autocomplete endpoint
                $.ajax({
                    url: '/inventory/api/items/autocomplete/',
                    dataType: 'json',
                    data: {
                        term: request.term,
                        current_item_id: currentItemId
                    },
                    success: function(data) {
                        // Transform the data for autocomplete
                        const items = data.results.map(function(item) {
                            return {
                                label: item.code ? `${item.code} - ${item.text}` : item.text,
                                value: item.code ? `${item.code} - ${item.text}` : item.text,
                                id: item.id,
                                code: item.code,
                                text: item.text,
                                type: item.type
                            };
                        });
                        response(items);
                    }
                });
            },
            minLength: 2,
            select: function(event, ui) {
                // Set the hidden input value to the selected item ID
                hiddenInput.value = ui.item.id;

                // Show the display field with the selected item
                if (ui.item.code) {
                    displayField.value = `${ui.item.code} - ${ui.item.text}`;
                } else {
                    displayField.value = ui.item.text;
                }

                // Show the display field and clear button
                displayField.style.display = 'block';
                clearButton.style.display = 'block';

                // Hide the search input
                input.style.display = 'none';

                // Prevent the default behavior
                return false;
            }
        }).autocomplete('instance')._renderItem = function(ul, item) {
            // Enhanced custom rendering for autocomplete items
            let html = '<div class="autocomplete-item">';

            if (item.code) {
                html += `<div><code>${item.code}</code> - <strong>${item.text}</strong></div>`;
                if (item.type) {
                    html += `<div class="item-type text-muted small">${item.type}</div>`;
                }
            } else {
                html += `<div><strong>${item.text}</strong></div>`;
                if (item.type) {
                    html += `<div class="item-type text-muted small">${item.type}</div>`;
                }
            }

            html += '</div>';

            return $('<li>')
                .append(html)
                .appendTo(ul);
        };

        // Handle clear button click
        clearButton.addEventListener('click', function() {
            // Clear the hidden input
            hiddenInput.value = '';

            // Clear and hide the display field
            displayField.value = '';
            displayField.style.display = 'none';

            // Hide the clear button
            clearButton.style.display = 'none';

            // Show and clear the search input
            input.style.display = 'block';
            input.value = '';
            input.focus();
        });
    }

    /**
     * Fetch item details for an initial value
     */
    function fetchItemDetails(itemId, displayField, clearButton) {
        if (!itemId) return;

        // Make AJAX request to get item details
        $.ajax({
            url: '/inventory/api/items/autocomplete/',
            dataType: 'json',
            data: {
                term: itemId,  // Use the ID as the search term
                exact_match: true
            },
            success: function(data) {
                if (data.results && data.results.length > 0) {
                    const item = data.results[0];

                    // Set the display field
                    if (item.code) {
                        displayField.value = `${item.code} - ${item.text}`;
                    } else {
                        displayField.value = item.text;
                    }

                    // Show the display field and clear button
                    displayField.style.display = 'block';
                    clearButton.style.display = 'block';

                    // Hide the search input
                    const input = document.getElementById(displayField.id.replace('_display', ''));
                    if (input) {
                        input.style.display = 'none';
                    }
                }
            }
        });
    }
});
