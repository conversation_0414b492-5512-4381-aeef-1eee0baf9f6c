import secrets
import io
import csv
import qrcode
import os
import tempfile
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse, JsonResponse, Http404
from django.core.paginator import Paginator
from django.urls import reverse
from django.db.models import Q
from django.utils.html import escape
from django.template.loader import render_to_string
from django.core.exceptions import ValidationError
from weasyprint import HTML, CSS
# Remove the FontConfiguration import as it's not available in this version
from .models import Item, ManagedListValue, Organization, ItemComment
from .forms import ItemSearchForm, ItemForm, LabelGenerationForm, ItemImageFormSet, MoveItemForm, ItemCommentForm, CommentEditForm
from .decorators import organization_permission_required
from PIL import Image
import base64
import logging
logger = logging.getLogger(__name__)
from django.contrib.auth import authenticate, login
from django.contrib.auth.forms import AuthenticationForm
from django.views.decorators.csrf import ensure_csrf_cookie
from django.views.decorators.cache import cache_control
from django.views.decorators.http import require_GET
from .utils.barcode_generator import generate_epc_barcode

@ensure_csrf_cookie
def custom_login(request):
    """Custom login view with better error handling"""
    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=username, password=password)
            if user is not None:
                login(request, user)
                # Try POST first, then GET, then default
                next_url = request.POST.get('next', '').strip()
                if not next_url:
                    next_url = request.GET.get('next', '').strip()
                if not next_url:
                    next_url = reverse('inventory:item_list')
                logger.info(f"User {username} logged in successfully, redirecting to {next_url}")
                return redirect(next_url)
            else:
                logger.warning(f"Failed login attempt for user {username}")
        else:
            logger.warning(f"Invalid login form: {form.errors}")
    else:
        form = AuthenticationForm()

    # Ensure we always have a proper next URL for the template
    next_url = request.GET.get('next', '').strip()
    if not next_url:
        next_url = reverse('inventory:item_list')

    return render(request, 'inventory/auth/login.html', {
        'form': form,
        'next': next_url
    })


@login_required
def item_list(request):
    """Display a list of items with search and filter options"""
    # Get current organization from session
    organization_id = request.session.get('org_id')

    # Require organization selection
    if not organization_id:
        messages.warning(request, "Please select an organization first")
        return redirect('inventory:select_organization')

    # Create the search form
    form = ItemSearchForm(request.GET, organization_id=organization_id)

    # Start with items from current organization only
    items = Item.objects.filter(organization_id=organization_id)

    # Apply additional filters based on form data
    if form.is_valid():
        # Apply search filter
        if form.cleaned_data.get('search'):
            search_query = form.cleaned_data['search']
            
            # Check if search query could be a hex value for RFID EPC
            is_hex = False
            try:
                if all(c in '0123456789ABCDEFabcdef' for c in search_query):
                    # It's a valid hex string
                    is_hex = True
            except:
                pass
                
            # Build the search query
            search_conditions = Q(item_name__icontains=search_query) | Q(item_description__icontains=search_query)
            

            
            # Apply the search conditions
            items = items.filter(search_conditions)

        # Apply other filters
        if form.cleaned_data.get('item_type'):
            items = items.filter(item_type=form.cleaned_data['item_type'])

        if form.cleaned_data.get('status'):
            items = items.filter(status=form.cleaned_data['status'])

        if form.cleaned_data.get('location'):
            items = items.filter(located_in=form.cleaned_data['location'])

        # Show/hide archived items
        if not form.cleaned_data.get('show_archived', False):
            items = items.filter(is_archived=False)
    else:
        # Default to showing only active items
        items = items.filter(is_archived=False)
        if form.errors:
            logger.debug(f"Form errors: {form.errors}")

    # Order items by name
    items = items.order_by('item_name')

    # Paginate the results
    paginator = Paginator(items, 25)  # Show 25 items per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'inventory/item_list.html', {
        'form': form,
        'page_obj': page_obj,
        'items': page_obj,
    })


def item_by_code(request, item_code):
    """
    Public view for an item by its item_code instead of primary key.
    No login required and no format validation.
    """
    # Try to find the item
    try:
        # Convert hex string to binary
        binary_code = bytes.fromhex(item_code.upper())

        # Use raw query to find the item
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute(
                "SELECT id FROM inventory_item WHERE item_code = %s",
                [binary_code]
            )
            result = cursor.fetchone()

        if not result:
            raise Item.DoesNotExist

        item = Item.objects.get(pk=result[0])

        # Get contained items for authenticated users
        contained_items = None
        if request.user.is_authenticated:
            contained_items = Item.objects.filter(
                located_in=item,
                is_archived=False
            ).order_by('item_name')

        # Get recent comments (10 most recent, excluding archived by default)
        show_archived = request.GET.get('show_archived', 'false').lower() == 'true'
        comments_query = ItemComment.objects.filter(item=item).select_related('user', 'reason', 'archived_by')

        if not show_archived:
            comments_query = comments_query.filter(is_archived=False)

        recent_comments = comments_query.order_by('-created_at')[:10]

        # Create comment form for both authenticated and guest users
        comment_form = ItemCommentForm(
            user=request.user if request.user.is_authenticated else None,
            organization_id=item.organization_id
        )

        # If user is authenticated, show the full detail view
        if request.user.is_authenticated:
            return render(request, 'inventory/item_detail.html', {
                'item': item,
                'contained_items': contained_items,
                'recent_comments': recent_comments,
                'comment_form': comment_form,
                'show_archived': show_archived,
            })
        else:
            # For unauthenticated users, show a simplified view with comments
            return render(request, 'inventory/item_public_view.html', {
                'item': item,
                'recent_comments': recent_comments,
                'comment_form': comment_form,
                'show_archived': show_archived,
            })
    except (ValueError, Item.DoesNotExist):
        # If item doesn't exist or invalid format, handle differently based on authentication
        if request.user.is_authenticated:
            # Logged-in users can create a new item with this code
            messages.info(request, f"No item found with code {item_code}. Create a new item with this code.")
            return redirect('inventory:add_item_with_code', item_code=item_code.upper())
        else:
            # Anonymous users see a "not found" page with login option
            return render(request, 'inventory/item_not_found.html', {
                'item_code': item_code.upper()
            })





@login_required
@organization_permission_required('add')
def add_item(request, item_code=None):
    # Get current organization from session or use default
    organization_id = request.session.get('org_id')

    # Require organization selection
    if not organization_id:
        messages.warning(request, "Please select an organization first")
        return redirect('inventory:select_organization')



    if request.method == 'POST':
        form = ItemForm(request.POST, request.FILES, organization_id=organization_id)

        if form.is_valid():
            # Create item but don't save yet
            item = form.save(commit=False)

            # Set organization
            item.organization_id = organization_id

            # Set item_code if provided
            if item_code:
                try:
                    item.item_code = item_code
                except ValueError:
                    messages.error(request, f"Invalid item code format: {item_code}")
                    return redirect('inventory:item_list')

            # Process custom fields
            custom_fields = {}
            for key, value in request.POST.items():
                if key.startswith('custom_field_') and not key.endswith('_new'):
                    field_name = key.replace('custom_field_', '')
                    custom_fields[field_name] = value

            item.custom_fields = custom_fields

            try:
                item.save()
                messages.success(request, f"Item {item.item_name} added successfully with code {item.item_code}.")
                return redirect('inventory:item_by_code', item_code=item.item_code)
            except ValidationError as e:
                messages.error(request, f"Error saving item: {e}")
        else:
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f"Error in {field}: {error}")
    else:
        form = ItemForm(organization_id=organization_id)

    return render(request, 'inventory/item_form.html', {
        'form': form,
        'is_add': True,
        'item_code': item_code,
    })


@login_required
@organization_permission_required('edit')
def edit_item(request, pk=None, item_code=None):
    # Get item by primary key or item_code
    if pk:
        item = get_object_or_404(Item, pk=pk)
    elif item_code:
        try:
            item = get_object_or_404(Item, _item_code=bytes.fromhex(item_code))
        except ValueError:
            messages.error(request, f"Invalid item code format: {item_code}")
            return redirect('inventory:item_list')
    else:
        raise Http404("Item not found")

    # Get current organization from session
    organization_id = request.session.get('org_id')

    if request.method == 'POST':
        form = ItemForm(request.POST, request.FILES, instance=item, organization_id=organization_id)

        if form.is_valid():
            # Create item but don't save yet
            item = form.save(commit=False)

            # Process custom fields
            custom_fields = {}
            for key, value in request.POST.items():
                if key.startswith('custom_field_') and not key.endswith('_new'):
                    field_name = key.replace('custom_field_', '')
                    custom_fields[field_name] = value

            item.custom_fields = custom_fields

            try:
                item.save()
                messages.success(request, f"Item {item.item_name} updated successfully.")
                return redirect('inventory:item_by_code', item_code=item.item_code)
            except ValidationError as e:
                messages.error(request, f"Error saving item: {e}")
        else:
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f"Error in {field}: {error}")
    else:
        form = ItemForm(instance=item, organization_id=organization_id)

    # Get custom fields from the item
    custom_fields = item.custom_fields if hasattr(item, 'custom_fields') else {}

    return render(request, 'inventory/item_form.html', {
        'form': form,
        'is_add': False,
        'item': item,
        'custom_fields': custom_fields,
    })


@login_required
def edit_item_by_code(request, item_code):
    """Edit an item by its item_code instead of primary key."""
    try:
        # Convert hex string to binary
        binary_code = bytes.fromhex(item_code.upper())

        # Use raw query to find the item
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute(
                "SELECT id FROM inventory_item WHERE item_code = %s",
                [binary_code]
            )
            result = cursor.fetchone()

        if not result:
            raise Item.DoesNotExist

        # Call the existing edit_item view with the found pk
        return edit_item(request, result[0])
    except (ValueError, Item.DoesNotExist):
        messages.error(request, f"No item found with code {item_code}.")
        return redirect('inventory:item_list')


@login_required
def archive_item(request, pk):
    item = get_object_or_404(Item, pk=pk)

    if request.method == 'POST':
        item.is_archived = True
        item.save()
        messages.success(request, f"Item {item.item_name} has been archived.")
        return redirect('inventory:item_list')

    return render(request, 'inventory/archive_confirm.html', {'item': item})


@login_required
def archive_item_by_code(request, item_code):
    """Archive an item by its item_code instead of primary key."""
    try:
        # Convert hex string to binary
        binary_code = bytes.fromhex(item_code.upper())

        # Use raw query to find the item
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute(
                "SELECT id FROM inventory_item WHERE item_code = %s",
                [binary_code]
            )
            result = cursor.fetchone()

        if not result:
            raise Item.DoesNotExist

        # Call the existing archive_item view with the found pk
        return archive_item(request, result[0])
    except (ValueError, Item.DoesNotExist):
        messages.error(request, f"No item found with code {item_code}.")
        return redirect('inventory:item_list')


@login_required
def item_lookup(request):
    if request.method == 'POST':
        item_code = request.POST.get('item_code', '').strip().upper()

        # Existing item_code lookup logic
        # Validate item_code format
        if len(item_code) != 6 or not all(c in '0123456789ABCDEF' for c in item_code):
            messages.error(request, "Invalid item code format. Must be 6 characters (0-9, A-F).")
            return render(request, 'inventory/item_lookup.html')

        # Convert hex string to binary
        binary_code = bytes.fromhex(item_code)

        # Check if item exists using raw query
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute(
                "SELECT id FROM inventory_item WHERE item_code = %s",
                [binary_code]
            )
            result = cursor.fetchone()

        if result:
            # Item found, redirect to detail page
            return redirect('inventory:item_detail', pk=result[0])
        else:
            # Item not found, redirect to create new item with this code
            messages.info(request, f"No item found with code {item_code}. Create a new item with this code.")
            return redirect('inventory:add_item_with_code', item_code=item_code)

    return render(request, 'inventory/item_lookup.html')


@login_required
def generate_labels(request):
    def generate_unique_codes(quantity):
        """Generate a specified number of unique item codes not in the database"""
        # Get existing codes from database
        existing_codes = set()
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT encode(item_code, 'hex') FROM inventory_item")
            for row in cursor.fetchall():
                if row[0]:
                    existing_codes.add(row[0].upper())

        # Generate unique codes
        generated_codes = []
        max_attempts = quantity * 3  # Allow for some collisions
        attempts = 0

        while len(generated_codes) < quantity and attempts < max_attempts:
            attempts += 1
            code = secrets.token_hex(3).upper()
            if code not in existing_codes and code not in generated_codes:
                generated_codes.append(code)

        return generated_codes

    if request.method == 'POST':
        # Option 1: Generate and download PDF of small QR codes
        if 'generate_pdf' in request.POST:
            # Generate 154 unique codes (fill the sheet)
            generated_codes = generate_unique_codes(154)

            if len(generated_codes) < 154:
                messages.warning(request, f"Could only generate {len(generated_codes)} unique codes.")

            # Generate QR codes with sopo.me URL
            try:
                # Use ReportLab for precise PDF generation
                from reportlab.lib.pagesizes import letter
                from reportlab.pdfgen import canvas
                import tempfile

                # Create a temporary file
                with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
                    tmp_path = tmp.name

                # Generate small round labels
                generate_small_round_labels(tmp_path, generated_codes)

                # Read the file and return it
                with open(tmp_path, 'rb') as f:
                    pdf_content = f.read()

                # Clean up the temporary file
                os.unlink(tmp_path)

                # Return PDF response
                response = HttpResponse(pdf_content, content_type='application/pdf')
                response['Content-Disposition'] = 'attachment; filename="qr_labels.pdf"'
                return response

            except Exception as e:
                # If PDF generation fails, log the error and show a message
                import logging
                import traceback
                logger = logging.getLogger(__name__)
                logger.error(f"PDF generation failed: {str(e)}")
                logger.error(traceback.format_exc())

                messages.error(request, f"PDF generation failed: {str(e)}")
                return redirect('inventory:generate_labels')

        # Option 3: Generate and download PDF of rectangular QR code labels
        if 'generate_rect_pdf' in request.POST:
            # Generate 80 unique codes (fill the sheet)
            generated_codes = generate_unique_codes(80)

            if len(generated_codes) < 80:
                messages.warning(request, f"Could only generate {len(generated_codes)} unique codes.")

            # Generate QR codes with sopo.me URL
            try:
                # Use ReportLab for precise PDF generation
                from reportlab.lib.pagesizes import letter
                from reportlab.pdfgen import canvas
                import tempfile

                # Create a temporary file
                with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
                    tmp_path = tmp.name

                # Generate rectangular labels
                generate_rectangular_labels(tmp_path, generated_codes)

                # Read the file and return it
                with open(tmp_path, 'rb') as f:
                    pdf_content = f.read()

                # Clean up the temporary file
                os.unlink(tmp_path)

                # Return PDF response
                response = HttpResponse(pdf_content, content_type='application/pdf')
                response['Content-Disposition'] = 'attachment; filename="rect_qr_labels.pdf"'
                return response

            except Exception as e:
                # If PDF generation fails, log the error and show a message
                import logging
                import traceback
                logger = logging.getLogger(__name__)
                logger.error(f"PDF generation failed: {str(e)}")
                logger.error(traceback.format_exc())

                messages.error(request, f"PDF generation failed: {str(e)}")
                return redirect('inventory:generate_labels')

        # Option 2: Generate and download CSV of codes and URLs
        if 'generate_csv' in request.POST:
            form = LabelGenerationForm(request.POST)
            if form.is_valid():
                quantity = form.cleaned_data['quantity']

                # Generate unique codes using the common function
                generated_codes = generate_unique_codes(quantity)

                if len(generated_codes) < quantity:
                    messages.warning(request, f"Could only generate {len(generated_codes)} unique codes.")

                # Create CSV file
                response = HttpResponse(content_type='text/csv')
                response['Content-Disposition'] = f'attachment; filename="qr_codes_{quantity}.csv"'

                writer = csv.writer(response)
                writer.writerow(['Code', 'URL'])

                for code in generated_codes:
                    # Use lowercase code in the URL
                    writer.writerow([code, f"http://sopo.me/{code.lower()}"])

                return response

    # Display the form for both options
    form = LabelGenerationForm()
    return render(request, 'inventory/generate_labels.html', {
        'form': form,
    })


@login_required
def add_item_with_code(request, item_code):
    return add_item(request, item_code=item_code)


@login_required
def item_detail(request, pk):
    """View details of a specific item"""
    item = get_object_or_404(Item, pk=pk)

    # Get contained items
    contained_items = Item.objects.filter(
        located_in=item,
        is_archived=False
    ).order_by('item_name')

    return render(request, 'inventory/item_detail.html', {
        'item': item,
        'contained_items': contained_items
    })


@login_required
@organization_permission_required('edit')
def move_item(request, pk=None, item_code=None):
    """Move an item to a new location/container"""
    # Get item by primary key or item_code
    if pk:
        item = get_object_or_404(Item, pk=pk)
    elif item_code:
        try:
            item = get_object_or_404(Item, _item_code=bytes.fromhex(item_code))
        except ValueError:
            messages.error(request, f"Invalid item code format: {item_code}")
            return redirect('inventory:item_list')
    else:
        raise Http404("Item not found")

    # Get current organization from session
    organization_id = request.session.get('org_id')

    if request.method == 'POST':
        form = MoveItemForm(request.POST, item=item, organization_id=organization_id)
        if form.is_valid():
            # Update the item's location
            item.located_in = form.cleaned_data['target_location']

            try:
                item.save()
                messages.success(request, f"Item {item.item_name} moved successfully.")
                return redirect('inventory:item_by_code', item_code=item.item_code)
            except ValidationError as e:
                messages.error(request, f"Error moving item: {e}")
        else:
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f"Error in {field}: {error}")
    else:
        form = MoveItemForm(item=item, organization_id=organization_id)

    return render(request, 'inventory/move_item.html', {
        'form': form,
        'item': item,
    })


@login_required
def test_pdf(request):
    """A simple view to test if WeasyPrint is working correctly"""
    try:
        # Create a simple HTML document
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Test PDF</title>
            <style>
                body { font-family: sans-serif; }
                h1 { color: blue; }
            </style>
        </head>
        <body>
            <h1>Test PDF</h1>
            <p>This is a test PDF generated by WeasyPrint.</p>
        </body>
        </html>
        """

        # Try a different approach with WeasyPrint
        from weasyprint import HTML

        # Create a temporary file
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
            tmp_path = tmp.name

        # Generate PDF to file
        HTML(string=html_content).write_pdf(tmp_path)

        # Read the file and return it
        with open(tmp_path, 'rb') as f:
            pdf_content = f.read()

        # Clean up the temporary file
        import os
        os.unlink(tmp_path)

        # Return PDF response
        response = HttpResponse(pdf_content, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="test.pdf"'
        return response
    except Exception as e:
        # Log the error details
        import logging
        import traceback
        logger = logging.getLogger(__name__)
        logger.error(f"PDF test failed: {str(e)}")
        logger.error(traceback.format_exc())

        # Return error response
        return HttpResponse(f"PDF generation failed: {str(e)}<br><pre>{traceback.format_exc()}</pre>",
                           content_type='text/html')

def generate_small_round_labels(output_path, generated_codes):
    """Generate a PDF with small round QR code labels"""
    from reportlab.lib.pagesizes import letter
    from reportlab.pdfgen import canvas

    # Label sheet specifications
    POINTS_PER_INCH = 72
    sheet_width, sheet_height = letter  # 8.5 x 11 inches

    # Base margins (define the printable area)
    left_margin = 0.25 * POINTS_PER_INCH
    top_margin = 0.375 * POINTS_PER_INCH
    right_margin = 0.25 * POINTS_PER_INCH
    bottom_margin = 0.375 * POINTS_PER_INCH

    # Calculate usable area based on margins
    usable_width = sheet_width - left_margin - right_margin
    usable_height = sheet_height - top_margin - bottom_margin

    # Grid positioning offsets (shift the entire grid without changing usable area)
    h_offset = 0.0 * POINTS_PER_INCH  # Positive moves right, negative moves left
    v_offset = 0.0 * POINTS_PER_INCH  # Positive moves down, negative moves up

    # Label specifications
    h_pitch = 0.75 * POINTS_PER_INCH  # Horizontal pitch (center to center)
    v_pitch = 0.75 * POINTS_PER_INCH  # Vertical pitch (center to center)
    label_diameter = 0.5 * POINTS_PER_INCH  # Label diameter
    qr_size = 0.28 * POINTS_PER_INCH  # QR code size (0.28")

    # Calculate exact number of columns and rows that fit
    cols = int(usable_width / h_pitch) + 1
    rows = int(usable_height / v_pitch) + 1

    # Create PDF canvas
    c = canvas.Canvas(output_path, pagesize=letter)
    c.setTitle("Small QR Labels")

    # Calculate starting positions for first label centers
    first_x = left_margin + (label_diameter/2) + h_offset
    first_y = sheet_height - top_margin - (label_diameter/2) + v_offset

    # Generate and place QR codes
    code_index = 0
    for row in range(rows):
        if code_index >= len(generated_codes):
            break

        # Calculate y position (from top down)
        y = first_y - (row * v_pitch)

        for col in range(cols):
            if code_index >= len(generated_codes):
                break

            # Calculate x position (from left to right)
            x = first_x + (col * h_pitch)

            # Generate QR code
            code = generated_codes[code_index].lower()
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=1,
            )
            qr.add_data(f"http://sopo.me/{code}")
            qr.make(fit=True)
            qr_img = qr.make_image(fill_color="black", back_color="white")

            # Save QR code to temporary file
            qr_tmp = io.BytesIO()
            qr_img.save(qr_tmp, format='PNG')
            qr_tmp.seek(0)

            # Create a temporary file for the QR code image
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as img_tmp:
                img_tmp_path = img_tmp.name
                img_tmp.write(qr_tmp.getvalue())

            # Place QR code on canvas using the file path (centered)
            c.drawImage(img_tmp_path, x - (qr_size/2), y - (qr_size/2), width=qr_size, height=qr_size)

            # Clean up the temporary image file
            os.unlink(img_tmp_path)

            # Add text below QR code - bigger font and closer positioning
            c.setFont("Courier", 4)  # Increased from 2.5 to 4 points
            text_width = c.stringWidth(code, "Courier", 4)
            c.drawString(x - (text_width/2), y - (qr_size/2) - 3, code)  # Reduced gap from 5 to 3 points

            code_index += 1

    # Save the PDF
    c.save()

def generate_rectangular_labels(output_path, generated_codes):
    """Generate a PDF with rectangular QR code labels"""
    from reportlab.lib.pagesizes import letter
    from reportlab.pdfgen import canvas

    # Label sheet specifications
    POINTS_PER_INCH = 72
    sheet_width, sheet_height = letter  # 8.5 x 11 inches

    # Rectangular label specifications (from provided dimensions)
    top_margin = 0.5 * POINTS_PER_INCH
    bottom_margin = 0.5 * POINTS_PER_INCH
    left_margin = 0.32812 * POINTS_PER_INCH
    right_margin = 0.32812 * POINTS_PER_INCH

    label_width = 1.75 * POINTS_PER_INCH
    label_height = 0.5 * POINTS_PER_INCH
    h_spacing = 0.28125 * POINTS_PER_INCH
    v_spacing = 0.0 * POINTS_PER_INCH

    h_pitch = 2.03125 * POINTS_PER_INCH  # Horizontal pitch (center to center)
    v_pitch = 0.5 * POINTS_PER_INCH  # Vertical pitch (center to center)

    # Fine-tuning offsets
    h_offset = 0.0 * POINTS_PER_INCH
    v_offset = 0.0 * POINTS_PER_INCH

    # Calculate usable area
    usable_width = sheet_width - left_margin - right_margin
    usable_height = sheet_height - top_margin - bottom_margin

    # Calculate number of columns and rows
    cols = 4  # Based on provided specs (80 labels per sheet, 20 rows)
    rows = 20  # Based on provided specs

    # Create PDF canvas
    c = canvas.Canvas(output_path, pagesize=letter)
    c.setTitle("Rectangular QR Labels")

    # QR code size (make it as tall as possible within the label)
    qr_size = label_height * 0.7  # Reduced from 0.9 to 0.8 (80% of label height)

    # Calculate starting positions for first label
    first_x = left_margin + h_offset
    first_y = sheet_height - top_margin + v_offset

    # Draw debugging outlines
    # Outline for usable area
    #c.setStrokeColorRGB(0.8, 0.8, 0.8)  # Light gray
    #c.rect(left_margin, bottom_margin, usable_width, usable_height, stroke=1, fill=0)

    # Generate and place QR codes with text
    code_index = 0
    for row in range(rows):
        if code_index >= len(generated_codes):
            break

        # Calculate y position for top-left corner of label
        y = first_y - (row * v_pitch)

        for col in range(cols):
            if code_index >= len(generated_codes):
                break

            # Calculate x position for top-left corner of label
            x = first_x + (col * h_pitch)

            # Draw rectangle outline for debugging
            #c.setStrokeColorRGB(0.9, 0.9, 0.9)  # Very light gray
            #c.rect(x, y - label_height, label_width, label_height, stroke=1, fill=0)

            # Generate QR code
            code = generated_codes[code_index].lower()
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=1,
            )
            qr.add_data(f"http://sopo.me/{code}")
            qr.make(fit=True)
            qr_img = qr.make_image(fill_color="black", back_color="white")

            # Save QR code to temporary file
            qr_tmp = io.BytesIO()
            qr_img.save(qr_tmp, format='PNG')
            qr_tmp.seek(0)

            # Create a temporary file for the QR code image
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as img_tmp:
                img_tmp_path = img_tmp.name
                img_tmp.write(qr_tmp.getvalue())

            # Place QR code on left side of label (centered vertically)
            qr_x = x + (label_height * 0.3)  # Small margin from left
            qr_y = y - label_height + ((label_height - qr_size) / 2)  # Centered vertically
            c.drawImage(img_tmp_path, qr_x, qr_y, width=qr_size, height=qr_size)

            # Clean up the temporary image file
            os.unlink(img_tmp_path)

            # Add text to right of QR code - lowercase as requested
            text_x = qr_x + qr_size + (label_height * 0.32)  # QR code + small spacing

            # Center text vertically
            # First get the font height (approximate)
            font_size = 20
            font_height = font_size * 0.8  # Approximate height based on font size
            text_y = y - (label_height / 2) - (font_height / 2)  # Center of label minus half of font height

            # Draw code in larger font (lowercase as requested)
            # Options for monospaced fonts: "Courier", "Courier-Bold", "Courier-Oblique", "Courier-BoldOblique"
            c.setFont("Courier", font_size)  # Changed from Courier-Bold to Courier
            c.drawString(text_x, text_y, code.lower())

            # No URL line - removed completely

            code_index += 1

    # Save the PDF
    c.save()


@login_required
def switch_organization(request, org_id):
    """Switch the current organization and redirect back to the previous page"""
    import logging
    logger = logging.getLogger(__name__)

    # Verify the organization exists and is active
    org = get_object_or_404(Organization, id=org_id, is_active=True)

    # Check if user has access to this organization
    from .models import OrganizationUser
    if not request.user.is_superuser:
        has_access = OrganizationUser.objects.filter(
            user=request.user,
            organization_id=org_id
        ).exists()
        if not has_access:
            logger.warning(f"User {request.user.username} attempted to switch to unauthorized organization {org_id}")
            messages.error(request, "You don't have access to that organization")
            return redirect('inventory:select_organization')

    # Log the session state before changes
    logger.info(f"Session BEFORE update: {dict(request.session)}")
    logger.info(f"Session key BEFORE: {request.session.session_key}")

    # Store in session - use direct dictionary access
    request.session['org_id'] = str(org_id)
    request.session.modified = True

    # Force an immediate save
    request.session.save()

    # Log the session state after changes
    logger.info(f"Session AFTER update: {dict(request.session)}")
    logger.info(f"Session key AFTER: {request.session.session_key}")

    # Get the next URL or default to item list
    next_url = request.POST.get('next', reverse('inventory:item_list'))

    # Add a message
    messages.success(request, f"Switched to {org.name}")

    # Use a proper Django redirect
    response = redirect(next_url)

    # Set a backup cookie in case session doesn't persist
    response.set_cookie('backup_org_id', str(org_id), max_age=86400*30)

    logger.info(f"Redirecting to: {next_url}")

    return response

def is_safe_url(url, allowed_hosts):
    """Check if the URL is safe to redirect to"""
    from django.utils.http import url_has_allowed_host_and_scheme
    return url_has_allowed_host_and_scheme(url, allowed_hosts=allowed_hosts)


@login_required
def select_organization(request):
    """Allow user to select which organization to work with"""
    # Get organizations the user has access to
    if request.user.is_superuser:
        organizations = Organization.objects.filter(is_active=True)
    else:
        organizations = Organization.objects.filter(
            organizationuser__user=request.user,
            is_active=True
        )

    # If no organizations, redirect to no_organizations view
    if not organizations.exists():
        return redirect('inventory:no_organizations')

    # If only one organization, select it automatically
    if organizations.count() == 1:
        org = organizations.first()
        request.session['org_id'] = str(org.id)
        messages.success(request, f"Automatically selected {org.name}")
        return redirect('inventory:item_list')

    # Get the next URL from query parameters or default to item list
    next_url = request.GET.get('next', reverse('inventory:item_list'))

    return render(request, 'inventory/select_organization.html', {
        'organizations': organizations,
        'next_url': next_url
    })

@login_required
@organization_permission_required('admin')
def manage_organization_users(request):
    """View to manage users in the current organization"""
    organization_id = request.session.get('org_id')
    organization = get_object_or_404(Organization, id=organization_id)

    # Get all organization users
    org_users = OrganizationUser.objects.filter(
        organization=organization
    ).select_related('user')

    return render(request, 'inventory/manage_organization_users.html', {
        'organization': organization,
        'org_users': org_users,
    })

@login_required
@organization_permission_required('admin')
def add_organization_user(request):
    """View to add a user to the current organization"""
    organization_id = request.session.get('org_id')
    organization = get_object_or_404(Organization, id=organization_id)

    if request.method == 'POST':
        # Get user by username or email
        username_or_email = request.POST.get('username_or_email')

        try:
            # Try to find user by username or email
            user = User.objects.get(
                Q(username=username_or_email) | Q(email=username_or_email)
            )

            # Check if user is already in organization
            if OrganizationUser.objects.filter(user=user, organization=organization).exists():
                messages.warning(request, f"User {user.username} is already a member of this organization")
            else:
                # Create organization user with specified permissions
                OrganizationUser.objects.create(
                    user=user,
                    organization=organization,
                    is_admin=request.POST.get('is_admin') == 'on',
                    can_edit=request.POST.get('can_edit') == 'on',
                    can_add=request.POST.get('can_add') == 'on'
                )
                messages.success(request, f"User {user.username} added to organization successfully")

        except User.DoesNotExist:
            messages.error(request, f"User with username or email '{username_or_email}' not found")

        return redirect('inventory:manage_organization_users')

    return render(request, 'inventory/add_organization_user.html', {
        'organization': organization,
    })

@login_required
@organization_permission_required('admin')
def edit_organization_user(request, user_id):
    """View to edit a user's permissions in the current organization"""
    organization_id = request.session.get('org_id')
    organization = get_object_or_404(Organization, id=organization_id)

    # Get organization user
    org_user = get_object_or_404(
        OrganizationUser,
        user_id=user_id,
        organization=organization
    )

    if request.method == 'POST':
        # Update permissions
        org_user.is_admin = request.POST.get('is_admin') == 'on'
        org_user.can_edit = request.POST.get('can_edit') == 'on'
        org_user.can_add = request.POST.get('can_add') == 'on'
        org_user.save()

        messages.success(request, f"Permissions updated for {org_user.user.username}")
        return redirect('inventory:manage_organization_users')

    return render(request, 'inventory/edit_organization_user.html', {
        'organization': organization,
        'org_user': org_user,
    })

@login_required
@organization_permission_required('admin')
def remove_organization_user(request, user_id):
    """View to remove a user from the current organization"""
    organization_id = request.session.get('org_id')
    organization = get_object_or_404(Organization, id=organization_id)

    # Get organization user
    org_user = get_object_or_404(
        OrganizationUser,
        user_id=user_id,
        organization=organization
    )

    # Don't allow removing yourself
    if org_user.user == request.user:
        messages.error(request, "You cannot remove yourself from the organization")
        return redirect('inventory:manage_organization_users')

    if request.method == 'POST':
        username = org_user.user.username
        org_user.delete()
        messages.success(request, f"User {username} removed from organization")
        return redirect('inventory:manage_organization_users')

    return render(request, 'inventory/confirm_remove_organization_user.html', {
        'organization': organization,
        'org_user': org_user,
    })

@login_required
def no_organizations(request):
    """View for users who don't have access to any organizations"""
    if not request.user.is_authenticated:
        return redirect('login')

    return render(request, 'inventory/no_organizations.html')

def serve_media_file(request, filename):
    """Directly serve a media file for testing"""
    import os
    from django.http import FileResponse, Http404
    from django.conf import settings

    # Construct the file path
    file_path = os.path.join(settings.MEDIA_ROOT, 'item_images', filename)

    # Check if the file exists
    if os.path.exists(file_path) and os.path.isfile(file_path):
        return FileResponse(open(file_path, 'rb'))
    else:
        raise Http404(f"Media file not found: {file_path}")


@login_required
def test_location_picker(request):
    """Test page for the location picker component"""
    return render(request, 'inventory/test_location_picker.html')


@login_required
def test_static_file(request):
    """Test if static files are being served correctly"""
    import os
    import time
    from django.http import HttpResponse
    from django.conf import settings

    # Build response with debug info
    response = "<html><body><h1>Static Files Debug Info</h1>"

    # Check settings
    response += "<h2>Django Settings</h2>"
    response += f"<p>STATIC_URL: {settings.STATIC_URL}</p>"
    response += f"<p>STATIC_ROOT: {settings.STATIC_ROOT}</p>"
    response += f"<p>DEBUG mode: {settings.DEBUG}</p>"

    # Check if our CSS file exists in the expected location
    css_path = os.path.join(settings.STATIC_ROOT, 'css/item_code_lookup.css')
    css_exists = os.path.exists(css_path)
    response += f"<p>CSS file exists at {css_path}: {css_exists}</p>"

    # Check app static directories
    app_static = os.path.join('inventory', 'static', 'css', 'item_code_lookup.css')
    app_static_exists = os.path.exists(app_static)
    response += f"<p>CSS file exists at app static directory {app_static}: {app_static_exists}</p>"

    # Add a test link with cache-busting
    timestamp = int(time.time())
    response += f'<p><a href="{settings.STATIC_URL}css/item_code_lookup.css?v={timestamp}" target="_blank">Test direct CSS link</a></p>'

    # Add Safari-specific instructions
    response += """
    <h2>Safari-Specific Troubleshooting</h2>
    <ol>
        <li>Clear Safari cache: Safari > Settings > Advanced > Show Develop menu, then Develop > Empty Caches (⌥⌘E)</li>
        <li>Disable caches during development: Develop > Disable Caches</li>
        <li>Check if CSS is loaded: Open Web Inspector (⌥⌘I), go to Network tab, reload page</li>
        <li>Try the direct CSS link above to see if it loads properly</li>
        <li><a href="/inventory/safari-test/">Go to Safari-specific test page</a></li>
    </ol>
    """

    response += "</body></html>"
    return HttpResponse(response)


@login_required
def safari_test(request):
    """Safari-specific CSS test page"""
    return render(request, 'inventory/safari_test.html')

@login_required
def item_autocomplete(request):
    """AJAX endpoint for item autocomplete by code or name"""
    search_term = request.GET.get('term', '').strip()
    organization_id = request.session.get('org_id')
    exact_match = request.GET.get('exact_match') == 'true'

    if not search_term or not organization_id:
        return JsonResponse({'results': []})

    # Get the current item ID if we're editing an existing item
    current_item_id = request.GET.get('current_item_id')

    # Check if we're looking for an exact match by ID
    if exact_match:
        try:
            # Try to get the item by ID
            item = Item.objects.get(pk=search_term, organization_id=organization_id)
            result = {
                'id': item.id,
                'text': item.item_name,
                'code': item.item_code,
                'type': item.item_type.value if item.item_type else "Unknown"
            }
            return JsonResponse({'results': [result]})
        except (Item.DoesNotExist, ValueError):
            return JsonResponse({'results': []})

    # Start with items from the current organization
    items = Item.objects.filter(
        organization_id=organization_id,
        is_archived=False
    )

    # Exclude the current item and its contained items if provided
    if current_item_id:
        try:
            current_item = Item.objects.get(pk=current_item_id)
            # Get all items contained within this item (directly or indirectly)
            contained_items = []

            def get_contained_items(item):
                direct_children = Item.objects.filter(located_in=item)
                result = list(direct_children)
                for child in direct_children:
                    result.extend(get_contained_items(child))
                return result

            contained_items = get_contained_items(current_item)
            exclude_ids = [current_item.id] + [item.id for item in contained_items]
            items = items.exclude(pk__in=exclude_ids)
        except Item.DoesNotExist:
            pass

    # Search by item code (exact match takes priority)
    if all(c in '0123456789ABCDEF' for c in search_term.upper()):
        # Try to find items with codes starting with the search term
        matching_items = []
        for item in items:
            if item.item_code and item.item_code.upper().startswith(search_term.upper()):
                matching_items.append(item)

        # If we found exact matches by code, prioritize those
        if matching_items:
            items = matching_items
        else:
            # Otherwise, search by name containing the term
            items = items.filter(item_name__icontains=search_term)
    else:
        # If not a valid hex code, search by name
        items = items.filter(item_name__icontains=search_term)

    # Limit results
    items = items[:20]

    # Format results
    results = []
    for item in items:
        results.append({
            'id': item.id,
            'text': f"{item.item_name}",
            'code': item.item_code,
            'type': item.item_type.value if item.item_type else "Unknown"
        })

    # Add "Top Level" option
    if not search_term or search_term.lower() in "top level":
        results.insert(0, {
            'id': '',
            'text': 'Top Level (No Container)',
            'code': '',
            'type': ''
        })

    return JsonResponse({'results': results})


@require_GET
@cache_control(max_age=3600)  # Cache for 1 hour
def item_barcode(request, item_code):
    """
    Generate and serve a barcode image for an item's EPC.

    Args:
        item_code: The item's hexadecimal code

    Query parameters:
        format: Barcode format ('code128', 'qr', or 'auto' for automatic selection)
        size: Size parameter (meaning depends on format)

    Returns:
        PNG image response with barcode
    """
    try:
        # Find the item by code
        binary_code = bytes.fromhex(item_code.upper())

        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute(
                "SELECT id FROM inventory_item WHERE item_code = %s",
                [binary_code]
            )
            result = cursor.fetchone()

        if not result:
            raise Http404("Item not found")

        item = get_object_or_404(Item, pk=result[0])

        # Check if item has EPC
        if not item.epc:
            raise Http404("Item has no EPC data")

        # Get query parameters
        barcode_format = request.GET.get('format', 'auto')
        if barcode_format == 'auto':
            barcode_format = None  # Let the generator decide

        # Size parameters
        size = request.GET.get('size', '200')
        try:
            size = int(size)
            size = max(100, min(500, size))  # Clamp between 100-500
        except ValueError:
            size = 200

        # Generate barcode
        try:
            buffer, format_used, metadata = generate_epc_barcode(
                item.epc,
                format_type=barcode_format,
                size=size,
                width=2,
                height=60
            )

            # Create HTTP response
            response = HttpResponse(buffer.getvalue(), content_type='image/png')
            response['Content-Disposition'] = f'inline; filename="epc_barcode_{item_code}.png"'

            # Add metadata headers
            response['X-Barcode-Format'] = format_used
            response['X-EPC-Data'] = item.epc
            response['X-Item-Code'] = item_code

            # Cache headers
            response['Cache-Control'] = 'public, max-age=3600'
            response['ETag'] = f'"{item.epc}-{format_used}-{size}"'

            return response

        except ValueError as e:
            logger.error(f"Barcode generation failed for item {item_code}: {e}")
            raise Http404(f"Cannot generate barcode: {e}")

    except ValueError:
        raise Http404("Invalid item code format")
    except Exception as e:
        logger.error(f"Unexpected error generating barcode for item {item_code}: {e}")
        raise Http404("Error generating barcode")


# Comment Views

def add_comment(request, item_code):
    """Add a comment to an item (supports both authenticated and guest users)"""
    import logging
    logger = logging.getLogger('inventory.comments')
    
    try:
        item = get_object_or_404(Item, _item_code=bytes.fromhex(item_code))
    except ValueError:
        messages.error(request, f"Invalid item code format: {item_code}")
        return redirect('inventory:item_list')

    # Get organization for form initialization
    organization_id = item.organization_id

    if request.method == 'POST':
        logger.info(f"Processing comment submission for item {item_code}")
        logger.info(f"User authenticated: {request.user.is_authenticated}")
        logger.info(f"User ID: {request.user.id if request.user.is_authenticated else 'None'}")
        
        # Create form with explicit user parameter
        form = ItemCommentForm(
            request.POST,
            request.FILES,
            user=request.user,  # Pass the user object directly
            organization_id=organization_id
        )

        if form.is_valid():
            try:
                comment = form.save(commit=False)
                comment.item = item
                
                # Double-check user assignment
                if request.user.is_authenticated:
                    comment.user = request.user
                    comment.guest_name = ''
                    logger.info(f"Explicitly setting user to {request.user.username} (ID: {request.user.id})")
                
                # Save with validation
                comment.full_clean()  # Validate before saving
                comment.save()
                
                logger.info(f"Comment saved with user ID: {comment.user.id if comment.user else 'None'}")
                logger.info(f"Comment saved with guest_name: '{comment.guest_name}'")
                
                messages.success(request, "Comment added successfully")
                return redirect('inventory:item_by_code', item_code=item_code)
            except ValidationError as e:
                logger.error(f"Validation error: {e}")
                for field, errors in e.message_dict.items():
                    for error in errors:
                        messages.error(request, f"Error in {field}: {error}")
                return redirect('inventory:item_by_code', item_code=item_code)
            except Exception as e:
                logger.error(f"Error saving comment: {str(e)}", exc_info=True)
                messages.error(request, f"Error saving comment: {str(e)}")
                return redirect('inventory:item_by_code', item_code=item_code)
        else:
            # Form has errors, add them as messages
            for field, errors in form.errors.items():
                for error in errors:
                    logger.warning(f"Form error in {field}: {error}")
                    messages.error(request, f"Error in {field}: {error}")
            
            # Redirect back to item detail page with errors
            return redirect('inventory:item_by_code', item_code=item_code)
    else:
        # For GET requests, redirect to item detail page
        return redirect('inventory:item_by_code', item_code=item_code)


@login_required
def edit_comment(request, comment_id):
    """Edit an existing comment (only by the original author)"""
    comment = get_object_or_404(ItemComment, pk=comment_id)

    # Check permissions - only the original author can edit
    if comment.user != request.user:
        messages.error(request, "You can only edit your own comments.")
        return redirect('inventory:item_by_code', item_code=comment.item.item_code)

    # Get organization for form initialization
    organization_id = comment.item.organization_id

    if request.method == 'POST':
        form = CommentEditForm(
            request.POST,
            request.FILES,
            instance=comment,
            organization_id=organization_id
        )

        if form.is_valid():
            form.save()
            messages.success(request, "Comment updated successfully.")
            return redirect('inventory:item_by_code', item_code=comment.item.item_code)
        else:
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f"Error in {field}: {error}")
    else:
        form = CommentEditForm(instance=comment, organization_id=organization_id)

    return render(request, 'inventory/edit_comment.html', {
        'form': form,
        'comment': comment,
        'item': comment.item,
    })


def recent_comments(request):
    """Display recent comments across all items with links to items"""
    # Get organization from session for authenticated users
    organization_id = None
    if request.user.is_authenticated:
        organization_id = request.session.get('org_id')
        if not organization_id:
            messages.warning(request, "Please select an organization first")
            return redirect('inventory:select_organization')

    # Build queryset
    show_archived = request.GET.get('show_archived', 'false').lower() == 'true'
    comments = ItemComment.objects.select_related('item', 'user', 'reason', 'archived_by').order_by('-created_at')

    # Filter by organization if user is authenticated
    if organization_id:
        comments = comments.filter(item__organization_id=organization_id)

    # Filter archived comments unless explicitly requested
    if not show_archived:
        comments = comments.filter(is_archived=False)

    # Paginate comments (20 per page)
    from django.core.paginator import Paginator
    paginator = Paginator(comments, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'inventory/recent_comments.html', {
        'page_obj': page_obj,
        'comments': page_obj.object_list,
        'show_archived': show_archived,
    })


def item_comments_ajax(request, item_code):
    """AJAX endpoint to get comments for an item"""
    try:
        item = get_object_or_404(Item, _item_code=bytes.fromhex(item_code))
    except ValueError:
        return JsonResponse({'error': 'Invalid item code'}, status=400)

    comments = ItemComment.objects.filter(item=item).select_related('user', 'reason').order_by('-created_at')[:10]

    comments_data = []
    for comment in comments:
        comments_data.append({
            'id': comment.id,
            'comment_text': comment.comment_text,
            'commenter_name': comment.commenter_name,
            'is_guest': comment.is_guest_comment,
            'reason': comment.reason.value if comment.reason else None,
            'photo_url': comment.photo.url if comment.photo else None,
            'created_at': comment.created_at.strftime('%Y-%m-%d %H:%M'),
            'is_edited': comment.is_edited,
            'can_edit': comment.can_edit(request.user) if request.user.is_authenticated else False,
        })

    return JsonResponse({'comments': comments_data})


@login_required
def archive_comment(request, comment_id):
    """Archive a comment (authenticated users only)"""
    import logging

    comment = get_object_or_404(ItemComment, pk=comment_id)
    logger = logging.getLogger('inventory.comments')

    # Check permissions
    if not comment.can_archive(request.user):
        logger.warning(
            f"Archive permission denied: User={request.user.username}, "
            f"CommentID={comment_id}, Item={comment.item.item_code}"
        )
        messages.error(request, "You don't have permission to archive this comment.")
        return redirect('inventory:item_by_code', item_code=comment.item.item_code)

    if request.method == 'POST':
        if comment.archive(request.user):
            messages.success(request, f"Comment archived successfully.")
            logger.info(
                f"Archive action initiated via view: User={request.user.username}, "
                f"CommentID={comment_id}, Item={comment.item.item_code}, "
                f"UserAgent={request.META.get('HTTP_USER_AGENT', 'Unknown')}"
            )
        else:
            messages.warning(request, "Comment was already archived.")
            logger.warning(
                f"Archive attempted on already archived comment: User={request.user.username}, "
                f"CommentID={comment_id}, Item={comment.item.item_code}"
            )

        # Redirect back to the item or recent comments page
        next_url = request.POST.get('next', request.META.get('HTTP_REFERER'))
        if next_url:
            return redirect(next_url)
        return redirect('inventory:item_by_code', item_code=comment.item.item_code)

    # Show confirmation page for GET requests
    return render(request, 'inventory/archive_comment_confirm.html', {
        'comment': comment,
        'item': comment.item,
    })


@login_required
def unarchive_comment(request, comment_id):
    """Unarchive a comment (authenticated users only)"""
    import logging

    comment = get_object_or_404(ItemComment, pk=comment_id)
    logger = logging.getLogger('inventory.comments')

    # Check permissions
    if not comment.can_archive(request.user):
        logger.warning(
            f"Unarchive permission denied: User={request.user.username}, "
            f"CommentID={comment_id}, Item={comment.item.item_code}"
        )
        messages.error(request, "You don't have permission to unarchive this comment.")
        return redirect('inventory:item_by_code', item_code=comment.item.item_code)

    if request.method == 'POST':
        if comment.unarchive():
            messages.success(request, f"Comment unarchived successfully.")
            logger.info(
                f"Unarchive action initiated via view: User={request.user.username}, "
                f"CommentID={comment_id}, Item={comment.item.item_code}, "
                f"UserAgent={request.META.get('HTTP_USER_AGENT', 'Unknown')}"
            )
        else:
            messages.warning(request, "Comment was not archived.")
            logger.warning(
                f"Unarchive attempted on non-archived comment: User={request.user.username}, "
                f"CommentID={comment_id}, Item={comment.item.item_code}"
            )

        # Redirect back to the item or recent comments page
        next_url = request.POST.get('next', request.META.get('HTTP_REFERER'))
        if next_url:
            return redirect(next_url)
        return redirect('inventory:item_by_code', item_code=comment.item.item_code)

    # Show confirmation page for GET requests
    return render(request, 'inventory/unarchive_comment_confirm.html', {
        'comment': comment,
        'item': comment.item,
    })

def logout_view(request):
    """Custom logout view that properly handles POST requests and redirects"""
    from django.contrib.auth import logout
    from django.contrib.auth.views import redirect_to_login
    from django.conf import settings
    
    if request.method == 'POST':
        # Properly log the user out
        logout(request)
        messages.success(request, "You have been successfully logged out.")
        
        # Redirect to login page
        login_url = settings.LOGIN_URL if hasattr(settings, 'LOGIN_URL') else '/accounts/login/'
        return redirect(login_url)
    else:
        # If accessed via GET, show a form with a logout button
        return render(request, 'inventory/logout_confirmation.html')

@login_required
def debug_session(request):
    """Debug view to check session state"""
    from django.http import JsonResponse
    
    # Get session data
    session_data = {
        'org_id': request.session.get('org_id'),
        'user_id': request.user.id,
        'username': request.user.username,
        'is_authenticated': request.user.is_authenticated,
        'session_key': request.session.session_key,
    }
    
    # Add all session keys (convert to dict for JSON serialization)
    all_keys = {}
    for key in request.session.keys():
        try:
            # Try to get the value and convert it to a serializable format
            value = request.session.get(key)
            # Check if value is serializable
            if isinstance(value, (str, int, float, bool, list, dict, type(None))):
                all_keys[key] = value
            else:
                all_keys[key] = str(value)
        except Exception as e:
            all_keys[key] = f"Error retrieving value: {str(e)}"
    
    session_data['all_keys'] = all_keys
    
    return JsonResponse(session_data)
