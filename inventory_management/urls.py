from django.contrib import admin
from django.urls import path, include, re_path
from django.conf import settings
from django.conf.urls.static import static
from django.contrib.auth import views as auth_views
from django.views.generic import RedirectView
from inventory.views import item_by_code, custom_login
from inventory.admin import org_admin
from django.views.static import serve

urlpatterns = [
    path('admin/', org_admin.urls),  # Custom admin site
    path('inventory/', include('inventory.urls')),

    # REST API
    path('api/v1/', include('inventory.api.urls')),

    # Authentication
    path('accounts/login/', custom_login, name='login'),
    path('accounts/logout/', auth_views.LogoutView.as_view(next_page='login'), name='logout'),

    # Redirect root URL to inventory
    path('', RedirectView.as_view(pattern_name='inventory:item_list', permanent=False)),

    # Explicit media serving
    path('media/<path:path>', serve, {'document_root': settings.MEDIA_ROOT}),

    # Ultra-short direct item access by code (4-8 character hex code)
    # Use re_path with a specific regex to avoid intercepting other URLs
    re_path(r'^(?P<item_code>[0-9a-fA-F]{4,8})$', item_by_code, name='direct_item_access'),
]

# Add static URLs for development
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
