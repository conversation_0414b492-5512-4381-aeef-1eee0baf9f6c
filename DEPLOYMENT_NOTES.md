# Deployment Notes

## Barcode Generation Dependencies

This application includes barcode generation functionality that requires both Python packages and system libraries.

### Python Dependencies

All Python dependencies are listed in `requirements.txt` and can be installed with:

```bash
pip install -r requirements.txt
```

### System Dependencies for DataMatrix Barcodes

The DataMatrix barcode functionality requires the native `libdmtx` library to be installed on the system.

#### Ubuntu/Debian:
```bash
sudo apt-get update
sudo apt-get install -y libdmtx0b libdmtx-dev
```

#### CentOS/RHEL/Fedora:
```bash
# For CentOS/RHEL 7/8
sudo yum install -y libdmtx libdmtx-devel

# For Fedora/newer versions
sudo dnf install -y libdmtx libdmtx-devel
```

#### Alpine Linux (Docker):
```bash
apk add --no-cache libdmtx libdmtx-dev
```

### Fallback Behavior

If the `libdmtx` system library is not available:

1. The application will automatically fall back to QR codes for 2D barcodes
2. A warning will be logged indicating DataMatrix support is unavailable
3. All other functionality will continue to work normally

### Docker Deployment

If deploying with Docker, add the system dependencies to your Dockerfile:

```dockerfile
# For Ubuntu/Debian base images
RUN apt-get update && apt-get install -y \
    libdmtx0b \
    libdmtx-dev \
    && rm -rf /var/lib/apt/lists/*

# For Alpine base images
RUN apk add --no-cache libdmtx libdmtx-dev
```

### Barcode Format Priority

The application uses the following priority for barcode formats:

1. **DataMatrix** (preferred) - Compact 2D barcode, ideal for EPC hex data
2. **QR Code** - Reliable 2D barcode with high error correction
3. **Code128** - Linear barcode fallback for compatibility

### Testing Barcode Functionality

To test that barcode generation is working correctly:

```bash
python manage.py shell
```

```python
from inventory.utils.barcode_generator import generate_epc_barcode, DATAMATRIX_AVAILABLE

print(f"DataMatrix available: {DATAMATRIX_AVAILABLE}")

# Test with sample EPC data
buffer, format_used, metadata = generate_epc_barcode("FC0000000000000000123456")
print(f"Generated format: {format_used}")
print(f"Buffer size: {len(buffer.getvalue())} bytes")
```

### Production Considerations

1. **Caching**: Barcode images are cached for 1 hour to improve performance
2. **Error Handling**: The application gracefully handles missing dependencies
3. **Logging**: Barcode generation errors and fallbacks are logged for monitoring
4. **Performance**: DataMatrix barcodes are typically smaller in file size than QR codes for hex data

### Troubleshooting

**Issue**: `ImportError: Unable to find dmtx shared library`
**Solution**: Install the system `libdmtx` library as described above

**Issue**: Barcodes not displaying on item detail pages
**Solution**: Check that the barcode endpoint is accessible: `/inventory/item/<item_code>/barcode/`

**Issue**: Only QR codes are generated instead of DataMatrix
**Solution**: Verify `libdmtx` is installed and `DATAMATRIX_AVAILABLE` is `True` in Django shell
